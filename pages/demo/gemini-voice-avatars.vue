<template>
  <div class="container mx-auto p-6">
    <UCard>
      <template #header>
        <h1 class="text-2xl font-bold">
          Gemini Voice Avatars Demo
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Custom SVG avatars for Gemini voices instead of country flags
        </p>
      </template>

      <div class="space-y-6">
        <!-- Sample Gemini Voices -->
        <div>
          <h3 class="text-lg font-semibold mb-4">
            Sample Gemini Voice Avatars
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="voice in sampleVoices"
              :key="voice.id"
              class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <div class="h-12 w-12 rounded-full overflow-hidden">
                  <img
                    :src="voice.icon"
                    :alt="voice.speaker_name"
                    class="w-full h-full object-cover"
                  >
                </div>
                <div>
                  <div class="font-medium">
                    {{ voice.speaker_name }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ voice.gender }} • {{ voice.age }} • {{ voice.accent }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Live Voice Library -->
        <div>
          <h3 class="text-lg font-semibold mb-4">
            Live Voice Library
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Select a voice and scroll to see sticky voice cards with new avatars
          </p>
          <VoicesLibraries :show-only-gemini-voices="true" />
        </div>

        <!-- Selected Voice Info -->
        <div v-if="selectedVoice">
          <h3 class="text-lg font-semibold mb-4">
            Currently Selected Voice
          </h3>
          <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="h-12 w-12 rounded-full overflow-hidden">
                <img
                  v-if="selectedVoice.type === 'gemini_voice' && selectedVoice.icon.startsWith('data:image/svg+xml')"
                  :src="selectedVoice.icon"
                  :alt="selectedVoice.speaker_name"
                  class="w-full h-full object-cover"
                >
                <UIcon
                  v-else
                  :name="selectedVoice.icon"
                  class="w-full h-full text-white"
                />
              </div>
              <div>
                <div class="font-medium">
                  {{ selectedVoice.speaker_name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ selectedVoice.gender }} • {{ selectedVoice.age }} • {{ selectedVoice.accent }}
                </div>
                <div class="text-xs text-gray-400">
                  Type: {{ selectedVoice.type }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import { useSpeechVoicesStore } from '~/stores/speechVoices'

const store = useSpeechVoicesStore()
const { selectedVoice } = useSpeechVoices()

// Sample voices for demonstration
const sampleVoices = computed(() => {
  const samples = [
    {
      id: 'demo-1',
      speaker_name: 'Alex Johnson',
      type: 'gemini_voice',
      gender: 'male',
      age: 'adult',
      accent: 'American',
      description: 'Professional and clear voice'
    },
    {
      id: 'demo-2',
      speaker_name: 'Sarah Chen',
      type: 'gemini_voice',
      gender: 'female',
      age: 'young',
      accent: 'British',
      description: 'Warm and friendly voice'
    },
    {
      id: 'demo-3',
      speaker_name: 'Marcus Williams',
      type: 'gemini_voice',
      gender: 'male',
      age: 'middle',
      accent: 'Australian',
      description: 'Deep and authoritative voice'
    },
    {
      id: 'demo-4',
      speaker_name: 'Emma Rodriguez',
      type: 'gemini_voice',
      gender: 'female',
      age: 'adult',
      accent: 'Spanish',
      description: 'Expressive and melodic voice'
    },
    {
      id: 'demo-5',
      speaker_name: 'David Kim',
      type: 'gemini_voice',
      gender: 'male',
      age: 'young',
      accent: 'Korean',
      description: 'Modern and energetic voice'
    },
    {
      id: 'demo-6',
      speaker_name: 'Isabella Rossi',
      type: 'gemini_voice',
      gender: 'female',
      age: 'middle',
      accent: 'Italian',
      description: 'Elegant and sophisticated voice'
    }
  ]

  return samples.map(voice => ({
    ...voice,
    icon: store.getVoiceIcon(voice)
  }))
})

definePageMeta({
  title: 'Gemini Voice Avatars Demo'
})
</script>

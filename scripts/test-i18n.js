#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

/**
 * Test i18n locale files after flattening
 */
function testI18nFiles() {
  const localesDir = path.join(__dirname, '../i18n/locales')
  const files = fs.readdirSync(localesDir).filter(file => file.endsWith('.json'))

  console.log('🧪 Testing i18n locale files...\n')

  let allTestsPassed = true
  const results = {}

  files.forEach((file) => {
    const filePath = path.join(localesDir, file)
    console.log(`Testing ${file}:`)

    try {
      // Test 1: JSON syntax validation
      const content = fs.readFileSync(filePath, 'utf8')
      const data = JSON.parse(content)
      console.log('  ✅ JSON syntax is valid')

      // Test 2: Check if all keys are flat (no nested objects)
      const hasNestedObjects = Object.values(data).some(value =>
        typeof value === 'object' && value !== null && !Array.isArray(value)
      )

      if (hasNestedObjects) {
        console.log('  ❌ Contains nested objects')
        allTestsPassed = false
      } else {
        console.log('  ✅ All keys are flat')
      }

      // Test 3: Check key count
      const keyCount = Object.keys(data).length
      console.log(`  ✅ Keys count: ${keyCount}`)

      // Test 4: Check for common required keys
      const requiredKeys = [
        'common.home',
        'nav.aitool',
        'generate',
        'cancel'
      ]

      const missingKeys = requiredKeys.filter(key => !data.hasOwnProperty(key))
      if (missingKeys.length > 0) {
        console.log(`  ⚠️  Missing common keys: ${missingKeys.join(', ')}`)
      } else {
        console.log('  ✅ Common keys present')
      }

      // Test 5: Check for empty values
      const emptyKeys = Object.entries(data)
        .filter(([key, value]) => !value || (typeof value === 'string' && value.trim() === ''))
        .map(([key]) => key)

      if (emptyKeys.length > 0) {
        console.log(`  ⚠️  Empty values found: ${emptyKeys.slice(0, 5).join(', ')}${emptyKeys.length > 5 ? '...' : ''}`)
      } else {
        console.log('  ✅ No empty values')
      }

      results[file] = {
        valid: true,
        keyCount,
        missingKeys,
        emptyKeys: emptyKeys.length
      }
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`)
      allTestsPassed = false
      results[file] = {
        valid: false,
        error: error.message
      }
    }

    console.log('')
  })

  // Summary
  console.log('📊 Summary:')
  console.log(`Total files tested: ${files.length}`)
  console.log(`Files with issues: ${Object.values(results).filter(r => !r.valid || (r.missingKeys && r.missingKeys.length > 0)).length}`)

  if (allTestsPassed) {
    console.log('🎉 All tests passed! i18n files are ready to use.')
  } else {
    console.log('⚠️  Some issues found. Please review the output above.')
  }

  return results
}

// Run tests
testI18nFiles()

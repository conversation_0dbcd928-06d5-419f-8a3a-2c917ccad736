<script setup lang="ts">
const toast = useToast()
const { t } = useI18n()

// State
const showApiKeyModal = ref(false)
const newlyCreatedApiKey = ref<any>(null)
const hasApiKeyCopied = ref(false)

// Mock data for testing
const mockApiKey = {
  id: 'test-123',
  name: 'Test API Key',
  api_key: 'sk-test-1234567890abcdef1234567890abcdef1234567890abcdef',
  created_at: new Date().toISOString()
}

function showTestModal() {
  newlyCreatedApiKey.value = mockApiKey
  hasApiKeyCopied.value = false
  showApiKeyModal.value = true
}

async function copyNewApiKey() {
  if (!newlyCreatedApiKey.value) return

  try {
    await navigator.clipboard.writeText(newlyCreatedApiKey.value.api_key)
    hasApiKeyCopied.value = true
    toast.add({
      title: t('success.copied') || 'Copied',
      description: t('apiKeys.copied') || 'API key copied to clipboard',
      color: 'success'
    })
  } catch {
    toast.add({
      title: t('error.general') || 'Error',
      description: t('apiKeys.copyError') || 'Failed to copy API key',
      color: 'error'
    })
  }
}

function closeApiKeyModal() {
  showApiKeyModal.value = false
  newlyCreatedApiKey.value = null
  hasApiKeyCopied.value = false
}
</script>

<template>
  <UPage class="min-h-screen dark:bg-neutral-900/80">
    <UContainer class="py-8">
      <div class="space-y-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Test API Key Modal
        </h1>

        <UButton @click="showTestModal">
          Show API Key Modal
        </UButton>
      </div>
    </UContainer>

    <!-- API Key Success Modal -->
    <UModal
      v-model:open="showApiKeyModal"
      :prevent-close="true"
    >
      <template #content>
        <UCard>
          <template #header>
            <div class="flex items-center space-x-2">
              <UIcon
                name="i-lucide-check-circle"
                class="w-5 h-5 text-success"
              />
              <h2 class="text-xl font-semibold">
                {{ $t("apiKeys.successTitle") || "API Key Created Successfully" }}
              </h2>
            </div>
          </template>

          <div class="space-y-6">
            <!-- Warning message -->
            <UAlert
              icon="i-lucide-alert-triangle"
              color="warning"
              variant="subtle"
              :title="t('apiKeys.importantNotice') || 'Important Notice'"
              :description="
                t('apiKeys.copyWarning')
                  || 'This is the only time you will be able to view and copy this API key. Please copy it now and store it securely.'
              "
            />

            <!-- API Key display -->
            <div class="space-y-3">
              <UFormGroup
                :label="t('apiKeys.name') || 'Name'"
              >
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ newlyCreatedApiKey?.name }}
                </div>
              </UFormGroup>

              <UFormGroup
                :label="t('apiKeys.key') || 'API Key'"
              >
                <div class="flex items-center space-x-2">
                  <code
                    class="flex-1 bg-gray-100 dark:bg-neutral-800 px-3 py-2 rounded font-mono text-sm break-all"
                  >
                    {{ newlyCreatedApiKey?.api_key }}
                  </code>
                  <UButton
                    variant="outline"
                    size="sm"
                    :icon="hasApiKeyCopied ? 'i-lucide-check' : 'i-lucide-copy'"
                    :disabled="hasApiKeyCopied"
                    @click="copyNewApiKey"
                  >
                    {{
                      hasApiKeyCopied
                        ? t("apiKeys.copied") || "Copied"
                        : t("common.copy") || "Copy"
                    }}
                  </UButton>
                </div>
              </UFormGroup>
            </div>

            <!-- Additional info -->
            <div class="text-sm text-gray-600 dark:text-gray-400">
              <p class="mb-2">
                {{ $t("apiKeys.securityTip") || "Security Tips:" }}
              </p>
              <ul class="list-disc list-inside space-y-1 ml-2">
                <li>{{ $t("apiKeys.tip1") || "Store this key in a secure location" }}</li>
                <li>{{ $t("apiKeys.tip2") || "Never share your API key publicly" }}</li>
                <li>{{ $t("apiKeys.tip3") || "If compromised, delete this key and create a new one" }}</li>
              </ul>
            </div>
          </div>

          <template #footer>
            <div class="flex justify-end w-full">
              <UButton
                :disabled="!hasApiKeyCopied"
                @click="closeApiKeyModal"
              >
                {{
                  hasApiKeyCopied
                    ? t("common.done") || "Done"
                    : t("apiKeys.copyFirst") || "Copy API Key First"
                }}
              </UButton>
            </div>
          </template>
        </UCard>
      </template>
    </UModal>
  </UPage>
</template>

<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-6">
      Test Auto Sync History
    </h1>

    <div class="space-y-4">
      <div>
        <h2 class="text-lg font-semibold mb-2">
          Active Syncs
        </h2>
        <div
          v-if="activeSyncs.length === 0"
          class="text-gray-500"
        >
          No active syncs
        </div>
        <div
          v-else
          class="space-y-2"
        >
          <div
            v-for="uuid in activeSyncs"
            :key="uuid"
            class="flex items-center justify-between p-3 bg-blue-50 rounded-lg"
          >
            <span class="font-mono text-sm">{{ uuid }}</span>
            <button
              class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
              @click="stopSync(uuid)"
            >
              Stop
            </button>
          </div>
        </div>
      </div>

      <div>
        <h2 class="text-lg font-semibold mb-2">
          Test Controls
        </h2>
        <div class="space-y-2">
          <div class="flex gap-2">
            <input
              v-model="testUuid"
              placeholder="Enter UUID to test"
              class="flex-1 px-3 py-2 border rounded"
            >
            <button
              :disabled="!testUuid || isAutoSyncing(testUuid)"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              @click="startTestSync"
            >
              Start Sync
            </button>
          </div>

          <button
            :disabled="activeSyncs.length === 0"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
            @click="stopAllSyncs"
          >
            Stop All Syncs
          </button>
        </div>
      </div>

      <div>
        <h2 class="text-lg font-semibold mb-2">
          Sync Logs
        </h2>
        <div class="bg-gray-100 p-4 rounded-lg h-64 overflow-y-auto">
          <div
            v-if="logs.length === 0"
            class="text-gray-500"
          >
            No logs yet
          </div>
          <div
            v-else
            class="space-y-1"
          >
            <div
              v-for="(log, index) in logs"
              :key="index"
              class="text-sm font-mono"
              :class="{
                'text-blue-600': log.type === 'info',
                'text-green-600': log.type === 'success',
                'text-red-600': log.type === 'error',
                'text-yellow-600': log.type === 'warning'
              }"
            >
              [{{ log.timestamp }}] {{ log.message }}
            </div>
          </div>
        </div>
        <button
          class="mt-2 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
          @click="clearLogs"
        >
          Clear Logs
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const {
  startAutoSync,
  stopAutoSync,
  stopAllAutoSyncs,
  isAutoSyncing,
  getActiveSyncs
} = useAutoSyncHistory()

const testUuid = ref('')
const logs = ref([])

const activeSyncs = computed(() => getActiveSyncs())

const addLog = (message, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push({ message, type, timestamp })

  // Keep only last 100 logs
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(-100)
  }
}

const startTestSync = () => {
  if (!testUuid.value) return

  addLog(`Starting auto sync for UUID: ${testUuid.value}`, 'info')

  startAutoSync({
    uuid: testUuid.value,
    intervalMs: 30000, // 30 seconds
    maxDurationMs: 300000, // 5 minutes
    targetStatuses: [2, 3], // Complete or Error
    onStatusChange: (status, historyDetail) => {
      addLog(`Status update for ${testUuid.value}: ${status}`, 'info')
      addLog(`History detail: ${JSON.stringify(historyDetail, null, 2)}`, 'info')
    },
    onComplete: (historyDetail) => {
      addLog(`Sync completed for ${testUuid.value}`, 'success')
      addLog(`Final result: ${JSON.stringify(historyDetail, null, 2)}`, 'success')
    },
    onError: (error) => {
      addLog(`Sync error for ${testUuid.value}: ${error.message || error}`, 'error')
    }
  })
}

const stopSync = (uuid) => {
  addLog(`Stopping sync for UUID: ${uuid}`, 'warning')
  stopAutoSync(uuid)
}

const stopAllSyncs = () => {
  addLog('Stopping all syncs', 'warning')
  stopAllAutoSyncs()
}

const clearLogs = () => {
  logs.value = []
}

// Add some sample UUIDs for testing
onMounted(() => {
  addLog('Auto sync test page loaded', 'info')
  addLog('You can test with sample UUIDs or real ones from generation', 'info')
})
</script>

<template>
  <UPage class="min-h-screen dark:bg-neutral-900/80">
    <UContainer class="py-8">
      <div class="space-y-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Test Custom Voice Registration Modal
        </h1>

        <UButton @click="showModal = true">
          Open Custom Voice Modal
        </UButton>

        <div class="max-w-4xl">
          <VoicesLibraries @select-voice="handleVoiceSelect" />
        </div>
      </div>
    </UContainer>

    <!-- Custom Voice Registration Modal -->
    <CustomVoiceRegistrationModal
      v-model="showModal"
      @voice-created="handleVoiceCreated"
    />
  </UPage>
</template>

<script setup lang="ts">
definePageMeta({
  layout: false
})

const showModal = ref(false)

const handleVoiceSelect = (voice: any) => {
  console.log('Selected voice:', voice)
}

const handleVoiceCreated = (voice: any) => {
  console.log('Voice created:', voice)
}
</script>

<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui'

const { t } = useI18n()
const route = useRoute()
const runtimeConfig = useRuntimeConfig()

const links = computed(() => {
  const baseLinks = [
    {
      label: t('User Info'),
      icon: 'i-lucide-user',
      to: '/profile',
      exact: true
    },
    {
      label: t('Security'),
      icon: 'i-lucide-shield',
      to: '/profile/security'
    }
  ]

  // Add Integration menu only if beta feature is disabled
  if (!runtimeConfig.public.features.beta) {
    baseLinks.push({
      label: t('Integration'),
      icon: 'i-lucide-puzzle',
      to: '/profile/integration',
      active: route.path.includes('/profile/integration')
    })
  }

  // Add credits and orders links only if beta feature is disabled
  if (!runtimeConfig.public.features.beta) {
    baseLinks.splice(1, 0,
      {
        label: t('Credits'),
        icon: 'mingcute:wallet-2-fill',
        to: '/profile/credits'
      },
      {
        label: t('Orders'),
        icon: 'mdi:clipboard-text-history-outline',
        to: '/profile/orders'
      }
    )
  }

  return [baseLinks,
    [
      // {
      //   label: 'Documentation',
      //   icon: 'i-lucide-book-open',
      //   to: 'https://ui.nuxt.com/getting-started/installation/pro/nuxt',
      //   target: '_blank'
      // },
      // {
      //   label: 'Buy now',
      //   icon: 'i-lucide-shopping-cart',
      //   to: 'https://ui.nuxt.com/pro/purchase',
      //   target: '_blank'
      // }
    ]
  ]
})
</script>

<template>
  <UPage>
    <UContainer class="max-w-screen-xl px-0">
      <UDashboardPanel
        id="settings"
        :ui="{ body: 'lg:py-12 sm:p-0' }"
      >
        <template #body>
          <div
            class="flex flex-col gap-4 px-4 sm:gap-6 lg:gap-12 w-full mx-auto"
          >
            <NuxtPage />
          </div>
        </template>
      </UDashboardPanel>
    </UContainer>
  </UPage>
</template>

<template>
  <div
    v-if="!runtimeConfig.public.features.beta"
    class="cursor-pointer flex items-center gap-1 text-xs dark:bg-primary-200/20 dark:hover:bg-primary-200/50 bg-primary-400/20 hover:bg-primary-400/50 px-3 py-1.5 rounded-md"
    @click="() => navigateTo('/profile/credits')"
  >
    <div class="!text-primary">
      <NumberAnimation
        :from="0"
        :to="user_credit?.available_credit || 0"
        :format="formatNumber"
        :duration="1"
        autoplay
      />
    </div>
    <div>
      {{ $t("Credits") }}
    </div>
    <UIcon
      name="ic:baseline-plus"
      class="!text-primary text-lg ml-auto"
    />
  </div>
</template>

<script setup lang="ts">
import NumberAnimation from 'vue-number-animation'

const authStore = useAuthStore()
const { user_credit } = storeToRefs(authStore)
const runtimeConfig = useRuntimeConfig()
</script>

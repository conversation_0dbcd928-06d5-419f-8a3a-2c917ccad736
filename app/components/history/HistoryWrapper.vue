<template>
  <div class="relative group h-52">
    <div
      class="absolute top-2 right-4 flex flex-col items-end gap-1"
    >
      <UTooltip :text="getTypeTooltip(type)">
        <span
          class="px-1 py-1 rounded z-10 flex items-center justify-center transition-all duration-200"
          :class="getTypeIconClass(type)"
        >
          <UIcon
            :name="getTypeIcon(type)"
            class="w-6 h-6"
          />
        </span>
      </UTooltip>
      <span
        v-if="status === 3"
        class="bg-red-500/90 text-white text-[0.625rem] px-1.5 py-0.5 rounded z-10 text-xs font-semibold flex items-center gap-1"
      >
        <UIcon
          name="material-symbols:error"
          class="w-3 h-3"
        />
        {{ $t("Error") }}
      </span>
    </div>

    <!-- Extend Menu Button (visible on hover) -->
    <div
      class="absolute bottom-2 right-2 z-20 group-hover:hidden sm:hidden block"
      :class="{
        // show onmobile
        '!block': hardShowMenu
      }"
    >
      <UButton
        v-if="!isOpeningMenu"
        icon="bi:three-dots"
        color="neutral"
        variant="soft"
        size="sm"
        class="w-full"
        @click="emits('menu')"
      />
    </div>

    <slot />
  </div>
</template>

<script setup lang="ts">
defineProps({
  type: {
    type: String,
    required: true
  },
  style: {
    type: String,
    default: ''
  },
  status: {
    type: Number,
    default: 1
  },
  id: {
    type: Number,
    required: true
  },
  hardShowMenu: {
    type: Boolean,
    default: false
  },
  isOpeningMenu: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['menu'])

// Function to get icon based on type
const getTypeIcon = (type: string): string => {
  const typeIconMap: Record<string, string> = {
    'image': 'hugeicons:ai-image',
    'video': 'hugeicons:ai-video',
    'tts-text': 'ri:voice-ai-line',
    'tts-document': 'basil:document-outline',
    'tts-multi-speaker': 'ri:chat-smile-ai-line',
    'speech': 'ri:voice-ai-line'
  }

  return typeIconMap[type] || 'material-symbols:help-outline'
}

// Function to get tooltip text based on type
const getTypeTooltip = (type: string): string => {
  const typeTooltipMap: Record<string, string> = {
    'image': 'Image',
    'video': 'Video',
    'tts-text': 'Audio',
    'tts-document': 'Audio',
    'tts-multi-speaker': 'Audio',
    'speech': 'Audio'
  }

  return typeTooltipMap[type] || 'Unknown'
}

// Function to get icon styling classes based on type
const getTypeIconClass = (type: string): string => {
  const typeClassMap: Record<string, string> = {
    'image': 'bg-blue-500/90 text-white shadow-lg',
    'video': 'bg-purple-500/90 text-white shadow-lg',
    'tts-text': 'bg-green-500/90 text-white shadow-lg',
    'tts-document': 'bg-green-500/90 text-white shadow-lg',
    'tts-multi-speaker': 'bg-green-500/90 text-white shadow-lg',
    'speech': 'bg-green-500/90 text-white shadow-lg'
  }

  return typeClassMap[type] || 'bg-gray-500/90 text-white shadow-lg'
}
</script>

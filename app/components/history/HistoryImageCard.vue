<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

const route = useRoute()
const router = useRouter()

const props = defineProps({
  orientation: {
    type: String,
    default: 'horizontal'
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const historyStore = useHistoryStore()
const { showDetailModal, historyDetail } = storeToRefs(historyStore)

const isFullScreenOpen = ref(false)
const isHovered = ref(false)
const isTouchDevice = ref(false)

// Check if it's a touch device on component mount
onMounted(() => {
  isTouchDevice.value
    = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  if (route.query.uuid && route.query.uuid === props.data.uuid) {
    isFullScreenOpen.value = true
  }
})

const openFullScreen = () => {
  // On touch devices, first tap shows overlay, second tap opens fullscreen
  if (isTouchDevice.value && !isHovered.value) {
    isHovered.value = true
    return
  }
  historyDetail.value = props.data as any
  showDetailModal.value = true
  // Update the URL to include the ID for navigation
  if (props.data.uuid) {
    router.push({ query: { ...route.query, uuid: props.data.uuid } })
  }
  isHovered.value = false
}

const firstImage = computed(() => {
  return props.data?.generated_image?.[0] || {}
})

const thumbnailImage = computed(() => {
  return props.data?.thumbnail_uri || props.data?.thumbnail_url
})

const title = computed(() => {
  return props.data?.input_text
})

const style = computed(() => {
  return firstImage.value?.style || props.data?.style || 'Unknown'
})

const menuClass = computed(() => {
  return 'opacity-0 group-hover:opacity-100 transition-opacity duration-300'
})

const showMenu = ref(false)
</script>

<template>
  <HistoryWrapper
    :id="data.id"
    :type="data.type"
    :style="style"
    :status="data.status"
    @menu="showMenu = true"
  >
    <UPageCard
      :orientation="'vertical'"
      :ui="{
        container: 'lg:items-start p-0 sm:p-0',
        root: 'overflow-hidden relative group cursor-pointer'
      }"
      class="h-full"
      @click="openFullScreen"
    >
      <div class="relative w-full h-full aspect-square sm:aspect-auto">
        <img
          v-if="thumbnailImage || firstImage?.image_url"
          :src="thumbnailImage || firstImage?.image_url"
          :alt="data.id"
          class="w-full object-center imagen cursor-pointer transition-opacity"
          @click="openFullScreen"
        >
        <div
          v-else
          class="w-full h-full flex items-center justify-center"
        >
          <div
            class="text-gray-400 dark:text-gray-600 flex flex-col items-center"
          >
            <UIcon
              name="i-lucide-image-off"
              class="w-8 h-8 mb-2"
            />
            {{ $t("noImageAvailable") }}
          </div>
        </div>
        <!-- Hover Overlay -->
        <div
          class="h-52 absolute inset-0 bg-gradient-to-b from-black/70 via-black/60 to-black/80 backdrop-blur-sm flex flex-col justify-between p-4"
          :class="{ 'opacity-100': isHovered || showMenu, [menuClass]: true }"
        >
          <div class="h-full flex flex-col">
            <div class="flex justify-between items-start gap-2">
              <div
                class="dark:text-white text-gray-100 font-medium text-xs line-clamp-1 cursor-pointer hover:underline hover:text-primary group-hover:translate-y-0"
                :class="{
                  'translate-y-0 opacity-100': isHovered || showMenu,
                  [menuClass]: true
                }"
                @click.stop="openFullScreen"
              >
                {{ title }}
              </div>
            </div>
            <div class="h-full">
              <HistoryMenu
                class="mt-auto"
                :data="data"
                @close="showMenu = false"
              />
            </div>
          </div>
        </div>
      </div>
    </UPageCard>
  </HistoryWrapper>
</template>

<style scoped>
.imagen {
  transition: transform 0.3s ease;
}

/* Apply scale effect to the image when the card is hovered */
:deep(.group:hover) .imagen {
  transform: scale(1.05);
}

/* For touch devices */
@media (hover: none) {
  :deep(.group:active) .imagen {
    transform: scale(1.05);
  }
}

/* Add line-clamp utility if not available in your Tailwind config */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation for modal content */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-scaleIn {
  animation: scaleIn 0.5s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>

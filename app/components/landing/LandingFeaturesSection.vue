<script setup lang="ts">
const { t } = useI18n()

const features = computed(() => [
  {
    icon: 'mingcute:ai-fill',
    title: t('features.ai.title'),
    description: t('features.ai.description')
  },
  {
    icon: 'lucide:zap',
    title: t('features.speed.title'),
    description: t('features.speed.description')
  },
  {
    icon: 'lucide:palette',
    title: t('features.creative.title'),
    description: t('features.creative.description')
  },
  {
    icon: 'lucide:monitor',
    title: t('features.quality.title'),
    description: t('features.quality.description')
  },
  {
    icon: 'lucide:users',
    title: t('features.collaboration.title'),
    description: t('features.collaboration.description')
  },
  {
    icon: 'lucide:download',
    title: t('features.export.title'),
    description: t('features.export.description')
  }
])
</script>

<template>
  <Motion
    :initial="{
      opacity: 0,
      y: 50
    }"
    :animate="{
      opacity: 1,
      y: 0
    }"
    :transition="{
      duration: 0.8
    }"
    class="mb-16"
  >
    <UPageSection
      :title="$t('Features That Set Us Apart')"
      :description="$t('features.subtitle')"
    >
      <UPageGrid>
        <Motion
          v-for="(feature, index) in features"
          :key="index"
          :initial="{
            opacity: 0,
            y: 50,
            scale: 0.9
          }"
          :animate="{
            opacity: 1,
            y: 0,
            scale: 1
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.1 + 0.2
          }"
        >
          <UPageCard
            v-bind="feature"
            spotlight
          />
        </Motion>
      </UPageGrid>

      <!-- Call to action at the bottom of features -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8,
          delay: 0.8
        }"
        class="text-center mt-16"
      >
        <div
          class="p-8 rounded-2xl bg-gradient-to-r from-primary/10 to-violet-500/10 border border-primary/20"
        >
          <h3 class="text-2xl font-bold mb-4">
            {{ t("features.cta.title") }}
          </h3>
          <p class="text-muted-foreground mb-6">
            {{ t("features.cta.description") }}
          </p>
          <UButton
            size="lg"
            color="primary"
            variant="solid"
            :label="t('features.cta.button')"
            to="/app"
            class="px-8 py-3"
            icon="mingcute:ai-fill"
          />
        </div>
      </Motion>
    </UPageSection>
  </Motion>
</template>

<style scoped>
/* Additional hover effects and transitions */
.group:hover {
  transform: translateY(-4px);
}

/* Gradient text effect for headings */
h2 {
  background: linear-gradient(
    135deg,
    rgb(var(--color-primary-500)),
    rgb(var(--color-violet-500))
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Card shadow animation */
@keyframes card-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(var(--color-primary-500), 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(var(--color-primary-500), 0.2);
  }
}

.group:hover {
  animation: card-glow 2s ease-in-out infinite;
}
</style>

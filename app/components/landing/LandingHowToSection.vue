<script setup lang="ts">
import type { StepperItem } from '@nuxt/ui'

const { t } = useI18n()

const steps = computed(
  () =>
    [
      {
        title: t('howto.step1.title'),
        description: t('howto.step1.description'),
        icon: 'lucide:edit-3',
        value: 1
      },
      {
        title: t('howto.step2.title'),
        description: t('howto.step2.description'),
        icon: 'lucide:settings',
        value: 2
      },
      {
        title: t('howto.step3.title'),
        description: t('howto.step3.description'),
        icon: 'mingcute:ai-fill',
        value: 3
      },
      {
        title: t('howto.step4.title'),
        description: t('howto.step4.description'),
        icon: 'lucide:download',
        value: 4
      }
    ] satisfies StepperItem[]
)

const examples = [
  {
    id: 1,
    title: t('howto.examples.example1.title'),
    prompt: t('howto.examples.example1.prompt'),
    video:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-cat-running.mp4',
    thumbnail:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-cat-running-thumb.jpg',
    category: 'Nature & Animals'
  },
  {
    id: 2,
    title: t('howto.examples.example2.title'),
    prompt: t('howto.examples.example2.prompt'),
    video:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-surfing.mp4',
    thumbnail:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-surfing-thumb.jpg',
    category: 'Sports & Action'
  },
  {
    id: 3,
    title: t('howto.examples.example3.title'),
    prompt: t('howto.examples.example3.prompt'),
    video:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-future-city.mp4',
    thumbnail:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-future-city-thumb.jpg',
    category: 'Sci-Fi & Fantasy'
  },
  {
    id: 4,
    title: t('howto.examples.example4.title'),
    prompt: t('howto.examples.example4.prompt'),
    video:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-dancing.mp4',
    thumbnail:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-dancing-thumb.jpg',
    category: 'People & Culture'
  },
  {
    id: 5,
    title: t('howto.examples.example5.title'),
    prompt: t('howto.examples.example5.prompt'),
    video:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-landscape.mp4',
    thumbnail:
      'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-landscape-thumb.jpg',
    category: 'Landscapes'
  }
]

const handleVideoHover = (index: number, isHovering: boolean) => {
  const video = document.getElementById(`video-${index}`) as HTMLVideoElement
  if (!video) return

  if (isHovering) {
    // Pause all other videos
    examples.forEach((_, i) => {
      if (i !== index) {
        const otherVideo = document.getElementById(
          `video-${i}`
        ) as HTMLVideoElement
        if (otherVideo) {
          otherVideo.pause()
        }
      }
    })

    // Play current video
    video.play().catch(() => {
      // Handle autoplay restrictions silently
    })
  } else {
    video.pause()
  }
}
</script>

<template>
  <UPageSection
    :title="$t('howto.title')"
    :description="$t('howto.subtitle')"
  >
    <!-- Steps -->
    <Motion
      :initial="{
        opacity: 0,
        y: 50
      }"
      :animate="{
        opacity: 1,
        y: 0
      }"
      :transition="{
        duration: 0.8,
        delay: 0.3
      }"
      class="mb-20"
    >
      <UStepper
        :items="steps"
        size="lg"
        color="primary"
        orientation="horizontal"
        disabled
        class="w-full"
        :ui="{
          header: 'flex flex-col sm:flex-row gap-4 sm:gap-2',
          separator: 'hidden sm:block'
        }"
      />
    </Motion>
  </UPageSection>
</template>

<style scoped>
/* Gradient text effect */
h2 {
  background: linear-gradient(
    135deg,
    rgb(var(--color-primary-500)),
    rgb(var(--color-violet-500))
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

h3 {
  background: linear-gradient(
    135deg,
    rgb(var(--color-primary-500)),
    rgb(var(--color-violet-500))
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Video hover effects */
video:hover {
  transform: scale(1.02);
}

/* Carousel custom styles */
:deep(.carousel-item) {
  transition: all 0.4s ease;
}

:deep(.carousel-item:hover) {
  transform: translateY(-8px);
}

/* Custom scrollbar for carousel */
:deep(.carousel-viewport) {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.carousel-viewport::-webkit-scrollbar) {
  display: none;
}

/* Enhanced carousel arrows */
:deep(.carousel-arrow) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

:deep(.carousel-arrow:hover) {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced card hover effects */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* Badge animation */
.badge-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Text shadow for better readability */
.drop-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.drop-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.6);
}

/* Enhanced video card animations */
.group:hover {
  transform: translateY(-4px);
}

/* Smooth gradient transitions */
.bg-gradient-to-t {
  background: linear-gradient(to top, var(--tw-gradient-stops));
}

/* Button hover enhancements */
.hover\:shadow-xl:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>

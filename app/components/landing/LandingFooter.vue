<script setup lang="ts">
const { t } = useI18n()

const links = [
  {
    title: t('footer.product.title'),
    items: [
      { label: t('footer.product.features'), to: '#features' },
      { label: t('footer.product.howto'), to: '#demo-section' },
      { label: t('footer.product.pricing'), to: '/pricing' },
      { label: t('footer.product.app'), to: '/app' }
    ]
  },
  {
    title: t('footer.company.title'),
    items: [
      { label: t('footer.company.about'), to: '/about' },
      { label: t('footer.company.blog'), to: '/blog' },
      { label: t('footer.company.careers'), to: '/careers' },
      { label: t('footer.company.contact'), to: '/contact' }
    ]
  },
  {
    title: t('footer.legal.title'),
    items: [
      { label: t('footer.legal.privacy'), to: '/privacy' },
      { label: t('footer.legal.terms'), to: '/terms' },
      { label: t('footer.legal.cookies'), to: '/cookies' }
    ]
  },
  {
    title: t('footer.support.title'),
    items: [
      { label: t('footer.support.help'), to: '/help' },
      { label: t('footer.support.api'), to: '/api-docs' },
      { label: t('footer.support.status'), to: '/status' }
    ]
  }
]

const socialLinks = [
  { icon: 'i-simple-icons-discord', to: 'https://discord.com/channels/1396217701449338972/1396219648298717225', label: 'Discord' },
  { icon: 'i-simple-icons-twitter', to: 'https://twitter.com/geminigen_ai', label: 'Twitter' },
  { icon: 'i-simple-icons-facebook', to: 'https://facebook.com/geminigen.ai', label: 'Facebook' },
  { icon: 'i-simple-icons-youtube', to: 'https://youtube.com/@geminigen', label: 'YouTube' }
]
</script>

<template>
  <footer class="bg-card border-t border-border">
    <UContainer class="py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
        <!-- Brand section -->
        <div class="lg:col-span-2">
          <Motion
            :initial="{
              opacity: 0,
              y: 20
            }"
            :animate="{
              opacity: 1,
              y: 0
            }"
            :transition="{
              duration: 0.6
            }"
          >
            <div class="flex items-center gap-3 mb-4">
              <BaseLogo class="w-8 h-8" />
              <span class="text-xl font-bold">GeminiGen AI</span>
            </div>
            <p class="text-muted-foreground mb-6 leading-relaxed">
              {{ t('footer.description') }}
            </p>

            <!-- Social links -->
            <div class="flex gap-4">
              <UButton
                v-for="social in socialLinks"
                :key="social.label"
                :icon="social.icon"
                :to="social.to"
                :aria-label="social.label"
                color="gray"
                variant="ghost"
                size="sm"
                target="_blank"
                class="hover:text-primary hover:bg-primary/10"
              />
            </div>
          </Motion>
        </div>

        <!-- Links sections -->
        <Motion
          v-for="(section, index) in links"
          :key="section.title"
          :initial="{
            opacity: 0,
            y: 20
          }"
          :animate="{
            opacity: 1,
            y: 0
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.1 + 0.2
          }"
          class="space-y-4"
        >
          <h3 class="font-semibold text-foreground">
            {{ section.title }}
          </h3>
          <ul class="space-y-2">
            <li
              v-for="item in section.items"
              :key="item.label"
            >
              <UButton
                :label="item.label"
                :to="item.to"
                variant="ghost"
                color="gray"
                size="sm"
                class="justify-start p-0 h-auto font-normal text-muted-foreground hover:text-primary"
              />
            </li>
          </ul>
        </Motion>
      </div>
    </UContainer>
  </footer>
</template>

<style scoped>
/* Hover effects for links */
a:hover {
  transition: color 0.2s ease;
}

/* Newsletter input focus effects */
.UInput:focus-within {
  ring: 2px solid rgb(var(--color-primary-500));
}
</style>

<script setup lang="ts">
interface DialogExample {
  id: string
  title: string
  stylePrompt: string
  dialogs: Array<{
    speaker: string
    text: string
  }>
  voice1: string
  voice2: string
  emotion?: string
  model: string
  duration: string
}

const emit = defineEmits<{
  'style-selected': [stylePrompt: string]
  'dialog-selected': [dialogs: Array<{ speaker: string, text: string }>]
}>()

// Sample dialog data with prompts and settings
const dialogExamples: DialogExample[] = [
  {
    id: '1',
    title: 'News Interview',
    stylePrompt: 'Professional news interview style with clear, authoritative delivery',
    dialogs: [
      { speaker: 'Voice 1', text: 'Good evening, I\'m here with <PERSON><PERSON> to discuss the latest breakthrough in renewable energy.' },
      { speaker: 'Voice 2', text: 'Thank you for having me. This discovery could revolutionize how we think about solar power efficiency.' },
      { speaker: 'Voice 1', text: 'Can you explain what makes this technology different from existing solar panels?' }
    ],
    voice1: 'Professional Anchor',
    voice2: 'Expert Guest',
    emotion: 'Confident',
    model: 'ElevenLabs',
    duration: '18s'
  },
  {
    id: '2',
    title: 'Customer Service',
    stylePrompt: 'Helpful and patient customer service conversation with empathetic tone',
    dialogs: [
      { speaker: 'Voice 1', text: 'Thank you for calling our support center. How can I help you today?' },
      { speaker: 'Voice 2', text: 'Hi, I\'m having trouble with my recent order. It hasn\'t arrived yet and I\'m getting worried.' },
      { speaker: 'Voice 1', text: 'I completely understand your concern. Let me look up your order right away and see what\'s happening.' }
    ],
    voice1: 'Support Agent',
    voice2: 'Customer',
    emotion: 'Helpful',
    model: 'ElevenLabs',
    duration: '15s'
  },
  {
    id: '3',
    title: 'Podcast Discussion',
    stylePrompt: 'Casual, engaging podcast conversation with natural flow and enthusiasm',
    dialogs: [
      { speaker: 'Voice 1', text: 'Welcome back to Tech Talk Tuesday! Today we\'re diving into the world of artificial intelligence.' },
      { speaker: 'Voice 2', text: 'I\'m so excited about this topic! AI has been transforming everything from healthcare to entertainment.' },
      { speaker: 'Voice 1', text: 'Absolutely! Let\'s start with how AI is changing the way we create content. What\'s your take on that?' }
    ],
    voice1: 'Podcast Host',
    voice2: 'Co-host',
    emotion: 'Enthusiastic',
    model: 'ElevenLabs',
    duration: '20s'
  },
  {
    id: '4',
    title: 'Educational Lesson',
    stylePrompt: 'Clear, instructional dialogue between teacher and student with patient explanation',
    dialogs: [
      { speaker: 'Voice 1', text: 'Today we\'re going to learn about photosynthesis. Can anyone tell me what plants need to make their own food?' },
      { speaker: 'Voice 2', text: 'They need sunlight, water, and... um... carbon dioxide?' },
      { speaker: 'Voice 1', text: 'Excellent! Those are exactly the three main ingredients. Now let\'s see how they work together.' }
    ],
    voice1: 'Teacher',
    voice2: 'Student',
    emotion: 'Educational',
    model: 'ElevenLabs',
    duration: '16s'
  },
  {
    id: '5',
    title: 'Storytelling Narration',
    stylePrompt: 'Dramatic storytelling with character voices and narrative flow',
    dialogs: [
      { speaker: 'Voice 1', text: 'In a kingdom far away, there lived a brave knight who had never faced a real challenge.' },
      { speaker: 'Voice 2', text: 'I must prove myself worthy of the round table, but how can I do that in times of peace?' },
      { speaker: 'Voice 1', text: 'Little did he know, his greatest adventure was about to begin with a mysterious letter.' }
    ],
    voice1: 'Narrator',
    voice2: 'Knight',
    emotion: 'Dramatic',
    model: 'ElevenLabs',
    duration: '22s'
  },
  {
    id: '6',
    title: 'Business Meeting',
    stylePrompt: 'Professional business discussion with confident and collaborative tone',
    dialogs: [
      { speaker: 'Voice 1', text: 'Let\'s review our quarterly results and discuss the strategy for next quarter.' },
      { speaker: 'Voice 2', text: 'The numbers look promising. We\'ve exceeded our targets by 15% this quarter.' },
      { speaker: 'Voice 1', text: 'That\'s excellent news. What factors do you think contributed most to this success?' }
    ],
    voice1: 'Manager',
    voice2: 'Team Lead',
    emotion: 'Professional',
    model: 'ElevenLabs',
    duration: '17s'
  }
]

const { t } = useI18n()

const handleStyleClick = (stylePrompt: string) => {
  emit('style-selected', stylePrompt)
}

const handleDialogClick = (dialogs: Array<{ speaker: string, text: string }>) => {
  emit('dialog-selected', dialogs)
}
</script>

<template>
  <div class="dialog-prompt-gallery">
    <div class="text-center mb-8">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        {{ t('Dialog Examples') }}
      </h2>
      <p class="text-gray-600 dark:text-gray-400">
        {{ t('Click on any example to use its style or dialog content') }}
      </p>
    </div>

    <div class="relative overflow-hidden">
      <!-- Gradient overlays for smooth edges -->
      <div class="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white dark:from-gray-900 to-transparent z-10 pointer-events-none" />
      <div class="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white dark:from-gray-900 to-transparent z-10 pointer-events-none" />

      <!-- Scrolling container -->
      <div class="flex gap-4 animate-marquee hover:pause-marquee">
        <!-- First set of examples -->
        <div
          v-for="example in dialogExamples"
          :key="example.id"
          class="flex-shrink-0 w-96 group"
        >
          <UCard
            :ui="{
              body: 'p-4',
              header: { padding: 'px-4 py-3' }
            }"
            class="h-full transition-all duration-300 hover:shadow-lg hover:scale-105 border border-gray-200 dark:border-gray-700"
          >
            <template #header>
              <div class="flex items-center gap-3">
                <div class="flex-shrink-0">
                  <UIcon
                    name="i-lucide-users"
                    class="text-2xl text-primary"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-gray-900 dark:text-white truncate">
                    {{ example.title }}
                  </h3>
                  <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{{ example.voice1 }} & {{ example.voice2 }}</span>
                    <span>•</span>
                    <span>{{ example.duration }}</span>
                  </div>
                </div>
              </div>
            </template>

            <div class="space-y-4">
              <!-- Style Description -->
              <div class="space-y-2">
                <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
                  {{ t('Style Description') }}
                </label>
                <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 leading-relaxed">
                  {{ example.stylePrompt }}
                </p>
                <UButton
                  variant="ghost"
                  size="xs"
                  :label="t('Use Style')"
                  icon="i-lucide-copy"
                  @click="handleStyleClick(example.stylePrompt)"
                />
              </div>

              <!-- Dialog Content -->
              <div class="space-y-2">
                <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
                  {{ t('Dialog Content') }}
                </label>
                <div class="space-y-2 max-h-32 overflow-y-auto">
                  <div
                    v-for="(dialog, index) in example.dialogs"
                    :key="index"
                    class="text-xs p-2 bg-gray-50 dark:bg-neutral-800 rounded"
                  >
                    <span class="font-medium text-primary">{{ dialog.speaker }}:</span>
                    <span class="ml-1">{{ dialog.text }}</span>
                  </div>
                </div>
                <UButton
                  variant="ghost"
                  size="xs"
                  :label="t('Use Dialog')"
                  icon="i-lucide-copy"
                  @click="handleDialogClick(example.dialogs)"
                />
              </div>

              <div class="flex items-center justify-between text-xs">
                <div class="flex items-center gap-2">
                  <UBadge
                    :label="example.model"
                    variant="soft"
                    color="primary"
                    size="xs"
                  />
                  <UBadge
                    v-if="example.emotion"
                    :label="example.emotion"
                    variant="soft"
                    color="gray"
                    size="xs"
                  />
                </div>

                <div class="flex items-center gap-1 text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                  <UIcon
                    name="i-lucide-mouse-pointer-click"
                    class="text-xs"
                  />
                  <span class="text-xs">{{ t('Click to use') }}</span>
                </div>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Duplicate set for seamless loop -->
        <div
          v-for="example in dialogExamples"
          :key="`duplicate-${example.id}`"
          class="flex-shrink-0 w-96 group"
        >
          <UCard
            :ui="{
              body: 'p-4',
              header: { padding: 'px-4 py-3' }
            }"
            class="h-full transition-all duration-300 hover:shadow-lg hover:scale-105 border border-gray-200 dark:border-gray-700"
          >
            <template #header>
              <div class="flex items-center gap-3">
                <div class="flex-shrink-0">
                  <UIcon
                    name="i-lucide-users"
                    class="text-2xl text-primary"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-gray-900 dark:text-white truncate">
                    {{ example.title }}
                  </h3>
                  <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{{ example.voice1 }} & {{ example.voice2 }}</span>
                    <span>•</span>
                    <span>{{ example.duration }}</span>
                  </div>
                </div>
              </div>
            </template>

            <div class="space-y-4">
              <!-- Style Description -->
              <div class="space-y-2">
                <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
                  {{ t('Style Description') }}
                </label>
                <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 leading-relaxed">
                  {{ example.stylePrompt }}
                </p>
                <UButton
                  variant="ghost"
                  size="xs"
                  :label="t('Use Style')"
                  icon="i-lucide-copy"
                  @click="handleStyleClick(example.stylePrompt)"
                />
              </div>

              <!-- Dialog Content -->
              <div class="space-y-2">
                <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
                  {{ t('Dialog Content') }}
                </label>
                <div class="space-y-2 max-h-32 overflow-y-auto">
                  <div
                    v-for="(dialog, index) in example.dialogs"
                    :key="index"
                    class="text-xs p-2 bg-gray-50 dark:bg-neutral-800 rounded"
                  >
                    <span class="font-medium text-primary">{{ dialog.speaker }}:</span>
                    <span class="ml-1">{{ dialog.text }}</span>
                  </div>
                </div>
                <UButton
                  variant="ghost"
                  size="xs"
                  :label="t('Use Dialog')"
                  icon="i-lucide-copy"
                  @click="handleDialogClick(example.dialogs)"
                />
              </div>

              <div class="flex items-center justify-between text-xs">
                <div class="flex items-center gap-2">
                  <UBadge
                    :label="example.model"
                    variant="soft"
                    color="primary"
                    size="xs"
                  />
                  <UBadge
                    v-if="example.emotion"
                    :label="example.emotion"
                    variant="soft"
                    color="gray"
                    size="xs"
                  />
                </div>

                <div class="flex items-center gap-1 text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                  <UIcon
                    name="i-lucide-mouse-pointer-click"
                    class="text-xs"
                  />
                  <span class="text-xs">{{ t('Click to use') }}</span>
                </div>
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 80s linear infinite;
}

.pause-marquee {
  animation-play-state: paused;
}

.dialog-prompt-gallery {
  @apply py-8;
}
</style>

<script setup lang="ts">
interface SpeechExample {
  id: string
  title: string
  prompt: string
  voice: string
  emotion?: string
  model: string
  duration: string
}

const emit = defineEmits<{
  'prompt-selected': [prompt: string]
}>()

// Sample speech data with prompts and settings
const speechExamples: SpeechExample[] = [
  {
    id: '1',
    title: 'News Anchor',
    prompt: 'Good evening, I\'m reporting live from the scene where a remarkable discovery has been made. Scientists have uncovered evidence that could change our understanding of ancient civilizations.',
    voice: 'Professional News Voice',
    emotion: 'Confident',
    model: 'ElevenLabs',
    duration: '12s'
  },
  {
    id: '2',
    title: 'Storyteller',
    prompt: 'Once upon a time, in a magical forest where the trees whispered secrets and the flowers sang lullabies, there lived a curious little fox who dreamed of adventures beyond the woodland.',
    voice: 'Warm Narrator',
    emotion: 'Gentle',
    model: 'ElevenLabs',
    duration: '15s'
  },
  {
    id: '3',
    title: 'Educational Content',
    prompt: 'The human brain contains approximately 86 billion neurons, each connected to thousands of others, creating a complex network that enables our thoughts, memories, and consciousness.',
    voice: 'Clear Teacher',
    emotion: 'Informative',
    model: 'ElevenLabs',
    duration: '10s'
  },
  {
    id: '4',
    title: 'Motivational Speaker',
    prompt: 'Every great achievement begins with a single step. Today is your opportunity to take that step, to push beyond your comfort zone, and to discover the incredible potential that lies within you.',
    voice: 'Inspiring Leader',
    emotion: 'Energetic',
    model: 'ElevenLabs',
    duration: '13s'
  },
  {
    id: '5',
    title: 'Meditation Guide',
    prompt: 'Take a deep breath in through your nose, feeling the air fill your lungs completely. Now slowly exhale through your mouth, releasing all tension and stress from your body.',
    voice: 'Calm Guide',
    emotion: 'Peaceful',
    model: 'ElevenLabs',
    duration: '11s'
  },
  {
    id: '6',
    title: 'Product Review',
    prompt: 'This innovative device combines cutting-edge technology with user-friendly design. The build quality is exceptional, and the performance exceeds expectations in every category we tested.',
    voice: 'Tech Reviewer',
    emotion: 'Analytical',
    model: 'ElevenLabs',
    duration: '12s'
  },
  {
    id: '7',
    title: 'Podcast Introduction',
    prompt: 'Welcome back to our weekly podcast where we explore the fascinating world of artificial intelligence and its impact on our daily lives. I\'m your host, and today we have an incredible guest.',
    voice: 'Podcast Host',
    emotion: 'Friendly',
    model: 'ElevenLabs',
    duration: '14s'
  },
  {
    id: '8',
    title: 'Customer Service',
    prompt: 'Thank you for calling our support center. I understand your concern and I\'m here to help you resolve this issue quickly and efficiently. Let me walk you through the solution step by step.',
    voice: 'Helpful Agent',
    emotion: 'Professional',
    model: 'ElevenLabs',
    duration: '13s'
  }
]

const { t } = useI18n()

const handlePromptClick = (prompt: string) => {
  emit('prompt-selected', prompt)
}
</script>

<template>
  <div class="speech-prompt-gallery">
    <div class="text-center mb-8">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        {{ t('Speech Examples') }}
      </h2>
      <p class="text-gray-600 dark:text-gray-400">
        {{ t('Click on any example to use its prompt for speech generation') }}
      </p>
    </div>

    <div class="relative overflow-hidden">
      <!-- Gradient overlays for smooth edges -->
      <div class="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white dark:from-gray-900 to-transparent z-10 pointer-events-none" />
      <div class="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white dark:from-gray-900 to-transparent z-10 pointer-events-none" />

      <!-- Scrolling container -->
      <div class="flex gap-4 animate-marquee hover:pause-marquee">
        <!-- First set of examples -->
        <div
          v-for="example in speechExamples"
          :key="example.id"
          class="flex-shrink-0 w-80 cursor-pointer group"
          @click="handlePromptClick(example.prompt)"
        >
          <UCard
            :ui="{
              body: 'p-4',
              header: { padding: 'px-4 py-3' }
            }"
            class="h-full transition-all duration-300 hover:shadow-lg hover:scale-105 border border-gray-200 dark:border-gray-700"
          >
            <template #header>
              <div class="flex items-center gap-3">
                <div class="flex-shrink-0">
                  <UIcon
                    name="i-lucide-mic"
                    class="text-2xl text-primary"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-gray-900 dark:text-white truncate">
                    {{ example.title }}
                  </h3>
                  <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{{ example.voice }}</span>
                    <span>•</span>
                    <span>{{ example.duration }}</span>
                  </div>
                </div>
              </div>
            </template>

            <div class="space-y-3">
              <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-4 leading-relaxed">
                {{ example.prompt }}
              </p>

              <div class="flex items-center justify-between text-xs">
                <div class="flex items-center gap-2">
                  <UBadge
                    :label="example.model"
                    variant="soft"
                    color="primary"
                    size="xs"
                  />
                  <UBadge
                    v-if="example.emotion"
                    :label="example.emotion"
                    variant="soft"
                    color="gray"
                    size="xs"
                  />
                </div>

                <div class="flex items-center gap-1 text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                  <UIcon
                    name="i-lucide-mouse-pointer-click"
                    class="text-xs"
                  />
                  <span class="text-xs">{{ t('Click to use') }}</span>
                </div>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Duplicate set for seamless loop -->
        <div
          v-for="example in speechExamples"
          :key="`duplicate-${example.id}`"
          class="flex-shrink-0 w-80 cursor-pointer group"
          @click="handlePromptClick(example.prompt)"
        >
          <UCard
            :ui="{
              body: 'p-4',
              header: { padding: 'px-4 py-3' }
            }"
            class="h-full transition-all duration-300 hover:shadow-lg hover:scale-105 border border-gray-200 dark:border-gray-700"
          >
            <template #header>
              <div class="flex items-center gap-3">
                <div class="flex-shrink-0">
                  <UIcon
                    name="i-lucide-mic"
                    class="text-2xl text-primary"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-gray-900 dark:text-white truncate">
                    {{ example.title }}
                  </h3>
                  <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{{ example.voice }}</span>
                    <span>•</span>
                    <span>{{ example.duration }}</span>
                  </div>
                </div>
              </div>
            </template>

            <div class="space-y-3">
              <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-4 leading-relaxed">
                {{ example.prompt }}
              </p>

              <div class="flex items-center justify-between text-xs">
                <div class="flex items-center gap-2">
                  <UBadge
                    :label="example.model"
                    variant="soft"
                    color="primary"
                    size="xs"
                  />
                  <UBadge
                    v-if="example.emotion"
                    :label="example.emotion"
                    variant="soft"
                    color="gray"
                    size="xs"
                  />
                </div>

                <div class="flex items-center gap-1 text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                  <UIcon
                    name="i-lucide-mouse-pointer-click"
                    class="text-xs"
                  />
                  <span class="text-xs">{{ t('Click to use') }}</span>
                </div>
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 60s linear infinite;
}

.pause-marquee {
  animation-play-state: paused;
}

.speech-prompt-gallery {
  @apply py-8;
}
</style>

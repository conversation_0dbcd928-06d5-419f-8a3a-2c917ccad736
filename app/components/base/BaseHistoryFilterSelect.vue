<script setup lang="ts">
interface HistoryFilterSelectProps {
  modelValue: string
}

const props = defineProps<HistoryFilterSelectProps>()
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const { t } = useI18n()

// Filter options for history
const historyFilterOptions = [
  {
    label: t('historyFilter.all'),
    value: 'all',
    icon: 'material-symbols:border-all-rounded'
  },
  {
    label: t('Image'),
    value: 'image',
    icon: 'hugeicons:ai-image'
  },
  {
    label: t('Video'),
    value: 'video',
    icon: 'hugeicons:ai-video'
  },
  {
    label: t('Text to Speech'),
    value: 'tts-text',
    icon: 'hugeicons:ai-voice'
  },
  {
    label: t('Document to Speech'),
    value: 'tts-document',
    icon: 'basil:document-outline'
  },
  {
    label: t('Dialogue'),
    value: 'tts-multi-speaker',
    icon: 'ri:chat-smile-ai-line'
  }
]

const updateValue = (value: string | number) => {
  emit('update:modelValue', String(value))
}
</script>

<template>
  <!-- Desktop version with text labels -->
  <UTabs
    :model-value="props.modelValue"
    :items="historyFilterOptions"
    class="hidden md:block w-fit"
    v-bind="$attrs"
    @update:model-value="updateValue"
  />

  <!-- Mobile version with icon buttons -->
  <div class="flex md:hidden gap-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg w-fit">
    <button
      v-for="option in historyFilterOptions"
      :key="option.value"
      :class="[
        'p-2 rounded-md transition-colors',
        props.modelValue === option.value
          ? 'bg-white dark:bg-gray-700 text-primary-500 shadow-sm'
          : 'text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-700'
      ]"
      :title="option.label"
      @click="updateValue(option.value)"
    >
      <UIcon
        :name="option.icon"
        class="w-5 h-5"
      />
    </button>
  </div>
</template>

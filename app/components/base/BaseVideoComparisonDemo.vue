<script setup lang="ts">
interface Props {
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px'
})

const currentExample = ref(0)
const examples = [
  {
    id: '1',
    type: 'text-to-video',
    beforeText:
      'A majestic eagle soaring through mountain peaks at sunset, golden hour lighting, cinematic shot',
    beforeImage: null,
    title: 'Text to Video Generation',
    description: 'Transform text descriptions into stunning videos with AI',
    afterContent: '/videos/example-1.mp4'
  },
  {
    id: '2',
    type: 'image-to-video',
    beforeText: null,
    beforeImage:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
    title: 'Image to Video Animation',
    description: 'Bring static images to life with AI video generation',
    afterContent: '/videos/example-2.mp4'
  },
  {
    id: '3',
    type: 'text-to-video',
    beforeText: 'Extreme close-up of a an eye with city reflected in it.',
    beforeImage: null,
    title: 'Composition',
    description: 'Create immersive videos from text descriptions',
    afterContent: '/videos/example-3.mp4'
  }
]

const sliderPosition = ref(50)
const isDragging = ref(false)
const containerRef = ref<HTMLElement>()
const isHovered = ref(false)

const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  updatePosition(event)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) {
    updatePosition(event)
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const updatePosition = (event: MouseEvent) => {
  if (!containerRef.value) return

  const rect = containerRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
  sliderPosition.value = percentage
}

const nextExample = () => {
  currentExample.value = (currentExample.value + 1) % examples.length
}

const prevExample = () => {
  currentExample.value
    = currentExample.value === 0 ? examples.length - 1 : currentExample.value - 1
}

// Auto-cycle examples
let autoTimer: NodeJS.Timeout | null = null

const startAutoTimer = () => {
  if (autoTimer) clearInterval(autoTimer)
  autoTimer = setInterval(() => {
    if (!isHovered.value) {
      nextExample()
    }
  }, 5000)
}

const stopAutoTimer = () => {
  if (autoTimer) {
    clearInterval(autoTimer)
    autoTimer = null
  }
}

// Handle mouse enter/leave events
const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

onMounted(() => {
  startAutoTimer()
})

onUnmounted(() => {
  stopAutoTimer()
})

const videoRef = ref<HTMLVideoElement>()
const videoLoaded = ref(false)
// Video event handlers
const onVideoLoaded = () => {
  videoLoaded.value = true
  if (videoRef.value) {
    videoRef.value.play().catch(() => {
      // Ignore autoplay errors
    })
  }
}
</script>

<template>
  <div
    class="relative w-full"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Text-to-Video Layout (Text overlay on video) -->
    <div
      v-if="examples[currentExample]?.beforeText"
      class="relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700"
      :style="{ height: props.height }"
    >
      <!-- Video Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600"
      >
        <video
          v-if="examples[currentExample]?.afterContent"
          ref="videoRef"
          :src="examples[currentExample]?.afterContent"
          class="w-full h-full object-cover"
          muted
          loop
          autoplay
          playsinline
          @loadeddata="onVideoLoaded"
        />

        <div class="w-full h-full flex items-center justify-center">
          <div class="text-center text-white">
            <UIcon
              name="i-lucide-video"
              class="w-16 h-16 mb-4 mx-auto opacity-80"
            />
            <p class="text-lg font-medium mb-2">
              Generated Video
            </p>
            <p class="text-sm opacity-80">
              AI-powered video generation
            </p>
          </div>
        </div>
      </div>

      <!-- Text Prompt Overlay -->
      <div class="absolute bottom-0 left-0 right-0 p-4">
        <div class="bg-black/40 backdrop-blur-sm rounded-lg p-4 mx-auto">
          <div class="text-center">
            <div class="flex items-center justify-center mb-2">
              <UIcon
                name="i-lucide-type"
                class="w-4 h-4 text-white mr-2"
              />
              <span class="text-xs font-medium text-white/80">Text Prompt</span>
            </div>
            <p class="text-white text-sm font-medium leading-relaxed">
              "{{ examples[currentExample]?.beforeText }}"
            </p>
          </div>
        </div>
      </div>

      <!-- Video Label -->
      <div
        class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium z-20"
      >
        Generated Video
      </div>

      <!-- Navigation Arrows for Text-to-Video -->
      <UButton
        v-if="examples.length > 1"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-30"
        @click.stop="prevExample"
      >
        <UIcon
          name="i-lucide-chevron-left"
          class="w-5 h-5"
        />
      </UButton>

      <UButton
        v-if="examples.length > 1"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-30"
        @click.stop="nextExample"
      >
        <UIcon
          name="i-lucide-chevron-right"
          class="w-5 h-5"
        />
      </UButton>
    </div>

    <!-- Image-to-Video Layout (Horizontal with Slider) -->
    <div
      v-else
      ref="containerRef"
      class="relative overflow-hidden rounded-lg cursor-ew-resize select-none border border-gray-200 dark:border-gray-700"
      :style="{ height: props.height }"
      @mousedown="handleMouseDown"
    >
      <!-- Right Side - Video -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600"
      >
        <video
          v-if="examples[currentExample]?.afterContent"
          ref="videoRef"
          :src="examples[currentExample]?.afterContent"
          class="w-full h-full object-cover"
          muted
          loop
          autoplay
          playsinline
          @loadeddata="onVideoLoaded"
        />

        <div class="w-full h-full flex items-center justify-center">
          <div class="text-center text-white">
            <UIcon
              name="i-lucide-video"
              class="w-16 h-16 mb-4 mx-auto opacity-80"
            />
            <p class="text-lg font-medium mb-2">
              Generated Video
            </p>
            <p class="text-sm opacity-80">
              AI-powered video generation
            </p>
          </div>
        </div>

        <!-- Video Label -->
        <div
          class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium"
        >
          Generated Video
        </div>
      </div>

      <!-- Left Side - Image (Clipped) -->
      <div
        class="absolute inset-0 overflow-hidden"
        :style="{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }"
      >
        <div class="w-full h-full">
          <img
            v-if="examples[currentExample]?.beforeImage"
            :src="examples[currentExample]?.beforeImage || ''"
            alt="Source Image"
            class="w-full h-full object-cover"
          >
        </div>

        <!-- Image Label -->
        <div
          class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium"
        >
          Source Image
        </div>
      </div>

      <!-- Slider Line -->
      <div
        class="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg z-10 pointer-events-none"
        :style="{ left: `${sliderPosition}%` }"
      >
        <!-- Slider Handle -->
        <div
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto"
        >
          <div
            class="w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center cursor-ew-resize"
          >
            <div class="flex space-x-0.5">
              <div class="w-0.5 h-4 bg-gray-400 rounded-full" />
              <div class="w-0.5 h-4 bg-gray-400 rounded-full" />
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Arrows for Image-to-Video -->
      <UButton
        v-if="examples.length > 1"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-30"
        @click.stop="prevExample"
        @mousedown.stop
      >
        <UIcon
          name="i-lucide-chevron-left"
          class="w-5 h-5"
        />
      </UButton>

      <UButton
        v-if="examples.length > 1"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-30"
        @click.stop="nextExample"
        @mousedown.stop
      >
        <UIcon
          name="i-lucide-chevron-right"
          class="w-5 h-5"
        />
      </UButton>

      <!-- Type Indicators for Image-to-Video -->
      <div class="absolute bottom-4 left-4">
        <div
          class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1"
        >
          <UIcon
            name="i-lucide-image"
            class="w-3 h-3"
          />
          <span>Image</span>
        </div>
      </div>

      <div class="absolute bottom-4 right-4">
        <div
          class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1"
        >
          <UIcon
            name="i-lucide-video"
            class="w-3 h-3"
          />
          <span>Video</span>
        </div>
      </div>
    </div>

    <!-- Example Info -->
    <div class="mt-4 text-center">
      <h4 class="text-base font-medium text-gray-900 dark:text-white mb-1">
        {{ examples[currentExample]?.title }}
      </h4>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        {{ examples[currentExample]?.description }}
      </p>
    </div>

    <!-- Dots Indicator -->
    <div
      v-if="examples.length > 1"
      class="flex justify-center mt-4 space-x-2"
    >
      <UButton
        v-for="(example, index) in examples"
        :key="example.id"
        class="w-2 h-2 rounded-full transition-all duration-200"
        :class="
          index === currentExample
            ? 'bg-primary-500'
            : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
        "
        @click="currentExample = index"
        @mousedown.stop
      />
    </div>

    <!-- Instructions -->
    <div class="mt-4 text-center">
      <p class="text-xs text-gray-500 dark:text-gray-400">
        <span v-if="examples[currentExample]?.beforeText">
          {{
            $t(
              "Text prompt is displayed as an overlay on the generated video for easy comparison."
            )
          }}
        </span>
        <span v-else>
          {{
            $t(
              "Drag the slider left and right to compare source images with generated videos. You can also click anywhere to move the slider."
            )
          }}
        </span>
      </p>
    </div>
  </div>
</template>

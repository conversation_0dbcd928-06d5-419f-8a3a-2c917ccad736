<script setup lang="ts">
const props = defineProps<{
  modelValue: any
}>()

const textToSpeechStore = useTextToSpeechStore()
const { selectedPrompt, userCustomPrompts, loadings, errors }
  = storeToRefs(textToSpeechStore)

const { t } = useI18n()
const isOpen = ref(false)
const promptName = ref(selectedPrompt.value?.label) as Ref<string | null>
const promptValue = ref(props.modelValue) as Ref<string>
const promptNameRef = ref()
const isSaveAsNewPrompt = ref(false)
const confirm = useConfirm()
const toast = useToast()

const emits = defineEmits(['update:modelValue'])
const onSelectPrompt = (prompt: any) => {
  // check if button is clicked
  if (prompt === null) {
    return
  }
  selectedPrompt.value = prompt
  promptValue.value = prompt.value
  promptName.value = prompt.label
}

const userCustomPromptsGroups = computed(() => {
  // Only show prompts if user is authenticated and account is verified
  if (!shouldLoadPrompts.value) {
    return []
  }

  const groups = [
    {
      id: 'prompts',
      label: t('Your saved prompts'),
      items: userCustomPrompts.value?.map((prompt: any, index: number) => ({
        id: prompt?.id,
        label: prompt.name,
        value: prompt.prompt,
        ...prompt
      }))
    }
  ] as any[]
  return groups
})

const onSaveCustomPrompt = () => {
  if (promptValue.value?.trim()) {
    isSaveAsNewPrompt.value = true
    if (!promptName.value) {
      promptName.value = t('Custom prompt {count}', {
        count: userCustomPrompts.value.length + 1
      })
    }
    nextTick(() => {
      promptNameRef.value?.input?.focus()
    })
  }
}

const onConfirmSaveCustomPrompt = async () => {
  if (promptValue.value?.trim() && promptName.value) {
    const newPrompt = {
      name: promptName.value,
      prompt: promptValue.value
    }
    const result = await textToSpeechStore.addNewPrompt(newPrompt)

    if (result) {
      toast.add({
        id: 'save-prompt',
        color: 'primary',
        title: t('Success'),
        description: t('Saved prompt successfully'),
        timeout: 3000,
        icon: 'i-ooui:success'
      })
      isSaveAsNewPrompt.value = false
    } else {
      // toast.add({
      //   id: 'save-prompt',
      //   color: 'red',
      //   title: t('Error'),
      //   description:
      //     t('Saved prompt failed')
      //     + '. '
      //     + t(errors.value.addNewPrompt?.data?.detail?.error_code || ''),
      //   timeout: 3000,
      //   icon: 'i-ooui:success'
      // })
    }
  }
}

const onConfirmUpdateCustomPrompt = async () => {
  if (promptValue.value?.trim() && promptName.value) {
    const newPrompt = {
      id: selectedPrompt.value?.id,
      label: promptName.value,
      value: promptValue.value
    }
    const result = await textToSpeechStore.updatePrompt(newPrompt)

    if (result) {
      toast.add({
        id: 'save-prompt',
        color: 'primary',
        title: t('Success'),
        description: t('Updated prompt successfully'),
        timeout: 3000,
        icon: 'i-ooui:success'
      })
      isSaveAsNewPrompt.value = false
      selectedPrompt.value = newPrompt
    } else {
      toast.add({
        id: 'save-prompt',
        color: 'red',
        title: t('Error'),
        description:
          t('Updated prompt failed')
          + '. '
          + t(errors.value.updatePrompt?.data?.detail?.error_code || ''),
        timeout: 3000,
        icon: 'i-ooui:success'
      })
    }
  }
}

const onUsePrompt = (promt?: any) => {
  if (promt?.value) {
    onSelectPrompt(promt)
  }
  if (promptValue.value?.trim()) {
    emits('update:modelValue', promptValue.value)

    isOpen.value = false
  }
}

const promptInputed = computed(() => {
  return selectedPrompt.value?.label || props.modelValue || t('Prompt')
})

const onCancelPrompt = () => {
  isOpen.value = false
  promptValue.value = ''
  promptName.value = null
  selectedPrompt.value = null
  emits('update:modelValue', '')
}

const onDeletePrompt = (prompt: any) => {
  confirm.openConfirm({
    title: t('Delete'),
    description: t('Are you sure you want to delete this prompt?'),
    confirmText: t('Delete'),
    cancelText: t('Cancel'),
    onConfirm: async () => {
      isOpen.value = true

      const resul = await textToSpeechStore.removePrompt(prompt)

      if (resul) {
        toast.add({
          id: 'delete-prompt',
          color: 'primary',
          title: t('Success'),
          description: t('Deleted prompt successfully'),
          timeout: 3000,
          icon: 'i-ooui:success'
        })
        // if prompt is selected, clear it
        if (selectedPrompt.value?.id === prompt?.id) {
          selectedPrompt.value = null
          promptValue.value = ''
          promptName.value = null
          emits('update:modelValue', '')
        }
      } else {
        toast.add({
          id: 'delete-prompt',
          color: 'red',
          title: t('Error'),
          description:
            t('Deleted prompt failed')
            + '. '
            + t(errors.value.removePrompt?.data?.detail?.error_code || ''),
          timeout: 3000,
          icon: 'i-ooui:success'
        })
      }
    }
  })
}

const authStore = useAuthStore()
const { isAuthenticated, isNotVerifyAccount } = storeToRefs(authStore)

// Only load prompts if user is authenticated and account is verified
const shouldLoadPrompts = computed(() => {
  return isAuthenticated.value && !isNotVerifyAccount.value
})

onMounted(() => {
  if (shouldLoadPrompts.value) {
    textToSpeechStore.fetchPrompts()
  }
})

// Watch for authentication changes and load prompts when user logs in
watch(shouldLoadPrompts, (newValue) => {
  if (newValue && userCustomPrompts.value.length === 0) {
    textToSpeechStore.fetchPrompts()
  }
})
</script>

<template>
  <div class="w-full">
    <UButton
      :icon="
        props.modelValue
          ? 'material-symbols:check-circle'
          : 'fluent:prompt-16-filled'
      "
      size="sm"
      color="neutral"
      variant="outline"
      :label="promptInputed"
      truncate
      :trailing-icon="'heroicons:chevron-down-20-solid'"
      class="w-full"
      :ui="{
        trailingIcon: 'ml-auto'
      }"
      @click="isOpen = true"
    />
    <UModal
      v-model:open="isOpen"
      :ui="{
        wrapper: 'max-h-[90vh] overflow-auto !scrollbar-thin'
      }"
    >
      <template #content>
        <div class="p-4 flex flex-col gap-4">
          <UFormField :label="$t('Custom prompt')">
            <UTextarea
              v-model="promptValue"
              autoresize
              :placeholder="$t('Enter your custom prompt here.')"
              class="w-full"
              :maxrows="7"
              :max-length="500"
              :ui="{
                base: 'scrollbar-thin'
              }"
            />

            <template #hint>
              <div class="text-xs">
                {{ promptValue.length }} / 500
              </div>
            </template>
          </UFormField>
          <UFormField
            v-if="isSaveAsNewPrompt || selectedPrompt"
            :label="$t('Prompt name')"
            :help="$t('This name will help you identify your prompt.')"
            :ui="{
              help: 'text-xs'
            }"
            class="w-full"
          >
            <UInput
              ref="promptNameRef"
              v-model="promptName"
              :placeholder="$t('Ex: Funny prompt')"
              :max-length="50"
              class="w-full"
            />
            <template #hint>
              <div class="text-xs">
                {{ promptName?.length || 0 }} / 50
              </div>
            </template>
          </UFormField>

          <div>
            <template v-if="isSaveAsNewPrompt">
              <div class="flex flex-row items-center justify-start gap-2">
                <UButton
                  :label="$t('Discard')"
                  size="xs"
                  icon="material-symbols:cancel-outline-rounded"
                  color="neutral"
                  variant="soft"
                  :disabled="loadings.addNewPrompt"
                  @click="isSaveAsNewPrompt = false"
                />
                <UButton
                  :disabled="!promptValue?.trim() || !promptName"
                  :label="$t('Ok, save it!')"
                  size="xs"
                  icon="bi:send-check"
                  color="primary"
                  :loading="loadings.addNewPrompt"
                  @click="onConfirmSaveCustomPrompt"
                />
              </div>
            </template>
            <template v-else>
              <div
                class="flex flex-row items-center justify-between flex-wrap space-y-2"
              >
                <div
                  v-if="shouldLoadPrompts"
                  class="flex flex-row items-center gap-2"
                >
                  <UButton
                    v-if="promptValue?.trim()"
                    :label="$t('Save as new')"
                    size="xs"
                    icon="material-symbols:add"
                    color="neutral"
                    variant="soft"
                    @click="onSaveCustomPrompt"
                  />

                  <UButton
                    v-if="selectedPrompt"
                    :label="$t('Update')"
                    size="xs"
                    icon="mdi:content-save-check"
                    color="neutral"
                    :loading="loadings.updatePrompt"
                    @click="onConfirmUpdateCustomPrompt"
                  />

                  <UButton
                    v-if="selectedPrompt"
                    :label="$t('Delete')"
                    size="xs"
                    color="neutral"
                    icon="material-symbols:delete-outline"
                    :ui="{
                      base: 'text-red-500'
                    }"
                    :loading="loadings.removePrompt"
                    @click="onDeletePrompt(selectedPrompt)"
                  />
                </div>
                <div class="flex flex-row items-center gap-2 ml-auto">
                  <UButton
                    :label="$t(`Don't use`)"
                    size="xs"
                    icon="material-symbols:cancel-outline"
                    color="neutral"
                    variant="soft"
                    @click="onCancelPrompt"
                  />
                  <UButton
                    v-if="promptValue?.trim()"
                    :label="$t('Use')"
                    size="xs"
                    icon="material-symbols:check-circle"
                    color="primary"
                    @click="onUsePrompt"
                  />
                </div>
              </div>
            </template>
          </div>
        </div>
        <UCommandPalette
          :model-value="selectedPrompt"
          :fuse="{
            resultLimit: 100,
            fuseOptions: {
              ignoreLocation: true,
              includeMatches: true,
              threshold: 0,
              keys: ['id', 'description']
            }
          }"
          nullable
          :close-button="{
            icon: 'i-heroicons-x-mark-20-solid',
            color: 'gray',
            variant: 'link',
            padded: false
          }"
          :groups="userCustomPromptsGroups"
          :ui="{
            group: {
              command: {
                base: 'group'
              }
            },
            container: 'scrollbar-thin'
          }"
          :loading="
            loadings.updatePrompt
              || loadings.removePrompt
              || loadings.addNewPrompt
              || loadings.fetchPrompts
          "
        >
          <template #empty>
            <div class="flex flex-col items-center justify-center h-full py-4">
              <UIcon
                :name="shouldLoadPrompts ? 'carbon:prompt-session' : 'material-symbols:login'"
                class="w-8 h-8 text-gray-400 mx-auto mb-2 opacity-50"
              />
              <p class="text-sm">
                {{ shouldLoadPrompts
                  ? $t("You don't have any saved prompts yet.")
                  : $t("Please login to access your saved prompts.")
                }}
              </p>
            </div>
          </template>
          <template #item="{ item: command }">
            <div
              class="w-full flex-1 flex flex-row items-center justify-between gap-2"
              @click="onSelectPrompt(command)"
            >
              <div class="flex flex-col hover:text-primary-500 text-left">
                <div class="text-sm font-semibold line-clamp-1">
                  {{ command.label }}
                </div>
                <div
                  class="text-xs text-gray-500 dark:text-gray-400 line-clamp-3"
                >
                  {{ command.value }}
                </div>
              </div>
              <div class="hidden flex-row items-center gap-2 group-hover:flex">
                <UButton
                  variant="outline"
                  :label="$t('Delete')"
                  size="xs"
                  icon="material-symbols:delete-outline"
                  color="neutral"
                  :ui="{
                    base: 'text-red-300'
                  }"
                  @click="onDeletePrompt(command)"
                />
                <UButton
                  :label="$t('Edit')"
                  size="xs"
                  icon="material-symbols:contract-edit-outline-sharp"
                  color="neutral"
                  variant="outline"
                  @click="onSelectPrompt(command)"
                />

                <UButton
                  :label="$t('Use')"
                  size="xs"
                  icon="material-symbols:check-circle"
                  color="neutral"
                  variant="outline"
                  :ui="{
                    base: 'text-primary-300'
                  }"
                  @click="onUsePrompt(command)"
                />
              </div>
            </div>
          </template>
        </UCommandPalette>
      </template>
    </UModal>
  </div>
</template>

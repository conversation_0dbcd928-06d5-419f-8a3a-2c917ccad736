<template>
  <div class="p-6 space-y-6">
    <h2 class="text-2xl font-bold">
      Speech Notification Demo
    </h2>

    <!-- Demo Controls -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">
          Demo Controls
        </h3>
      </template>

      <div class="space-y-4">
        <div class="flex gap-4">
          <UButton
            color="primary"
            @click="simulateLoadingState"
          >
            Simulate Loading State
          </UButton>

          <UButton
            color="success"
            @click="simulateSuccessState"
          >
            Simulate Success State
          </UButton>

          <UButton
            color="error"
            @click="simulateErrorState"
          >
            Simulate Error State
          </UButton>
        </div>

        <div class="flex gap-4">
          <UButton
            color="neutral"
            @click="simulateSpeechNotification"
          >
            Simulate Speech Notification
          </UButton>

          <UButton
            color="neutral"
            @click="resetDemo"
          >
            Reset Demo
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- Demo HistorySpeechCard -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">
          HistorySpeechCard Demo
        </h3>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <HistorySpeechCard
          :data="demoData"
          :loading="demoLoading"
        />
      </div>
    </UCard>

    <!-- Demo AIToolSpeechCard -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">
          AIToolSpeechCard Demo
        </h3>
      </template>

      <AIToolSpeechCard
        :data="demoData"
        :loading="demoLoading"
        :audio-url="demoData.generated_audio?.[0]?.audio_url"
        :prompt="demoData.input_text"
        :model="demoData.model_name"
        :voice="demoData.voice"
      />
    </UCard>

    <!-- Demo State Display -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">
          Current Demo State
        </h3>
      </template>

      <pre class="text-sm bg-gray-100 dark:bg-neutral-800 p-4 rounded">{{ JSON.stringify(demoData, null, 2) }}</pre>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const demoLoading = ref(false)
const demoData = ref({
  id: 'demo-1',
  uuid: 'demo-uuid-1',
  type: 'speech',
  status: 2, // 1 = processing, 2 = completed, 3 = error
  input_text: 'This is a demo speech generation prompt for testing the notification system.',
  model_name: 'tts-flash',
  voice: 'Gemini Voice',
  voice_id: 'gemini-voice-1',
  error_message: null,
  generated_audio: [
    {
      audio_url: 'https://example.com/demo-audio.mp3'
    }
  ],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
})

const simulateLoadingState = () => {
  demoLoading.value = true
  demoData.value.status = 1
  demoData.value.generated_audio = []
  demoData.value.error_message = null

  // Auto-complete after 3 seconds
  setTimeout(() => {
    simulateSuccessState()
  }, 3000)
}

const simulateSuccessState = () => {
  demoLoading.value = false
  demoData.value.status = 2
  demoData.value.generated_audio = [
    {
      audio_url: 'https://example.com/demo-audio.mp3'
    }
  ]
  demoData.value.error_message = null
}

const simulateErrorState = () => {
  demoLoading.value = false
  demoData.value.status = 3
  demoData.value.generated_audio = []
  demoData.value.error_message = 'Demo error: Speech generation failed due to invalid input parameters.'
}

const simulateSpeechNotification = () => {
  // Simulate receiving a speech notification
  const event = new CustomEvent('speechNotificationReceived', {
    detail: {
      historyDetail: {
        ...demoData.value,
        status: 2,
        generated_audio: [
          {
            audio_url: 'https://example.com/notification-audio.mp3'
          }
        ]
      },
      notification: {
        id: 'notif-1',
        type: 'speech',
        status: 2,
        uuid: demoData.value.uuid
      }
    }
  })

  window.dispatchEvent(event)

  // Update demo data to show the result
  simulateSuccessState()
}

const resetDemo = () => {
  demoLoading.value = false
  demoData.value.status = 2
  demoData.value.generated_audio = [
    {
      audio_url: 'https://example.com/demo-audio.mp3'
    }
  ]
  demoData.value.error_message = null
}
</script>

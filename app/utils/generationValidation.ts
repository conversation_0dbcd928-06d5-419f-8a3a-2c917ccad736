/**
 * Utility functions for generation validation and change detection
 */

export interface ValidationRule {
  isValid: boolean
  message?: string
}

export type ValidationFunction = () => ValidationRule

/**
 * Deep comparison utility for arrays and objects
 */
export const deepEqual = (a: any, b: any): boolean => {
  if (a === b) return true

  if (a == null || b == null) return a === b

  if (typeof a !== typeof b) return false

  if (typeof a !== 'object') return a === b

  if (Array.isArray(a) !== Array.isArray(b)) return false

  if (Array.isArray(a)) {
    if (a.length !== b.length) return false
    return a.every((item, index) => deepEqual(item, b[index]))
  }

  const keysA = Object.keys(a)
  const keysB = Object.keys(b)

  if (keysA.length !== keysB.length) return false

  return keysA.every(key => deepEqual(a[key], b[key]))
}

/**
 * Compare two image arrays for changes
 */
export const compareImageArrays = (current: any[], initial: any[]): boolean => {
  if (current.length !== initial.length) return true

  return current.some((img, index) => {
    const initialImg = initial[index]
    return !initialImg || img.src !== initialImg.src || img.alt !== initialImg.alt
  })
}

/**
 * Compare two file arrays for changes
 */
export const compareFileArrays = (current: any[], initial: any[]): boolean => {
  if (current.length !== initial.length) return true

  return current.some((file, index) => {
    const initialFile = initial[index]
    return !initialFile || file.name !== initialFile.name || file.size !== initialFile.size
  })
}

/**
 * Compare dialog arrays for changes (optimized version)
 */
export const compareDialogArrays = (current: any[], initial: any[]): boolean => {
  if (current.length !== initial.length) return true

  return current.some((dialog, index) => {
    const initialDialog = initial[index]
    return !initialDialog
      || dialog.speaker !== initialDialog.speaker
      || dialog.text !== initialDialog.text
      || dialog.voice_id !== initialDialog.voice_id
  })
}

/**
 * Common validation rules
 */
export const commonValidationRules = {
  requiredText: (value: string, message: string): ValidationFunction => () => ({
    isValid: !!value?.trim(),
    message
  }),

  requiredValue: (value: any, message: string): ValidationFunction => () => ({
    isValid: !!value,
    message
  }),

  requiredArray: (value: any[], message: string): ValidationFunction => () => ({
    isValid: value && value.length > 0,
    message
  }),

  requiredMultiple: (values: any[], message: string): ValidationFunction => () => ({
    isValid: values.every(value => !!value),
    message
  })
}

/**
 * Batch validation runner
 */
export const runValidations = (rules: ValidationFunction[]): ValidationRule => {
  for (const rule of rules) {
    const result = rule()
    if (!result.isValid) {
      return result
    }
  }

  return { isValid: true }
}

/**
 * Create a debounced hasChanges computed for better performance
 */
export const createDebouncedHasChanges = (
  compareFn: () => boolean,
  delay: number = 100
) => {
  let timeoutId: NodeJS.Timeout | null = null
  const debouncedValue = ref(false)

  const updateValue = () => {
    debouncedValue.value = compareFn()
  }

  const debouncedUpdate = () => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(updateValue, delay)
  }

  // Watch for changes and debounce updates
  watchEffect(() => {
    compareFn() // Trigger reactivity
    debouncedUpdate()
  })

  return readonly(debouncedValue)
}

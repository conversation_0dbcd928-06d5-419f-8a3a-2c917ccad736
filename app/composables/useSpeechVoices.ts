export interface SpeechVoice {
  id: string
  type: string
  used_credit: number
  speaker_name: string
  premium: boolean
  created_at: string
  gender: string
  training_type: string
  updated_at: string
  age: string
  user_id: string | null
  accent: string
  user_model_path: string | null
  description: string
  embedding_path: string | null
  audio_path: string | null
  status: number
  speaker_id: number
  sample_audio_path: string
  training_status: string | null
  is_favorite: boolean
  icon: string
}

export function useSpeechVoices() {
  const store = useSpeechVoicesStore()
  const $nuxtApp = useNuxtApp()
  const t = $nuxtApp.$i18n.t

  // Initialize store on first use
  onMounted(() => {
    store.initialize()
  })

  const voiceAccents = () => [
    {
      value: 'American',
      label: t('American'),
      icon: 'i-emojione-flag-for-united-states',
      languages: ['english']
    },
    {
      value: 'British',
      label: t('British'),
      icon: 'i-emojione-flag-for-united-kingdom',
      languages: ['english']
    },
    {
      value: 'Australian',
      label: t('Australian'),
      icon: 'i-emojione-flag-for-australia',
      languages: ['english']
    },
    {
      value: 'Indian',
      label: t('Indian'),
      icon: 'i-emojione-flag-for-india',
      languages: ['english']
    },
    {
      value: 'Chinese',
      label: t('Chinese'),
      icon: 'i-emojione-flag-for-china',
      languages: ['chinese']
    },
    {
      value: 'Spanish',
      label: t('Spanish'),
      icon: 'i-emojione-flag-for-spain',
      languages: ['spanish']
    },
    // Canaddian
    {
      value: 'Canadian',
      label: t('Canadian'),
      icon: 'i-emojione-flag-for-canada',
      languages: ['english']
    },
    {
      // irish
      value: 'Irish',
      label: t('Irish'),
      icon: 'i-emojione-flag-for-ireland',
      languages: ['english']
    },
    {
      // singaporean
      value: 'Singaporean',
      label: t('Singaporean'),
      icon: 'i-emojione-flag-for-singapore',
      languages: ['english']
    },
    {
      // russian
      value: 'Russian',
      label: t('Russian'),
      icon: 'i-emojione-flag-for-russia',
      languages: ['russian']
    },
    {
      // german
      value: 'German',
      label: t('German'),
      icon: 'i-emojione-flag-for-germany',
      languages: ['german']
    },
    {
      // portuguese
      value: 'Portuguese',
      label: t('Portuguese'),
      icon: 'i-emojione-flag-for-portugal',
      languages: ['portuguese']
    },
    {
      // hindi
      value: 'Hindi',
      label: t('Hindi'),
      icon: 'i-emojione-flag-for-india',
      languages: ['hindi']
    },
    {
      // mexican
      value: 'Mexican',
      label: t('Mexican'),
      icon: 'i-emojione-flag-for-mexico',
      languages: ['spanish']
    },
    {
      // latin american
      value: 'Latin American',
      label: t('Latin American'),
      icon: 'i-emojione-flag-for-mexico',
      languages: ['spanish']
    },
    {
      // argentine
      value: 'Argentine',
      label: t('Argentine'),
      icon: 'i-emojione-flag-for-argentina',
      languages: ['spanish']
    },
    {
      // peninsular
      value: 'Peninsular',
      label: t('Peninsular'),
      icon: 'i-emojione-flag-for-spain',
      languages: ['spanish']
    },
    {
      // peninsular
      value: 'Peninsular',
      label: t('Peninsular'),
      icon: 'i-emojione-flag-for-spain',
      languages: ['spanish']
    },
    {
      // french
      value: 'French',
      label: t('French'),
      icon: 'i-emojione-flag-for-france',
      languages: ['french']
    },
    {
      // parisian
      value: 'Parisian',
      label: t('Parisian'),
      icon: 'i-emojione-flag-for-france',
      languages: ['french']
    },
    {
      // standard
      value: 'Standard',
      label: t('Standard'),
      icon: 'i-emojione-flag-for-france',
      languages: ['french']
    },
    {
      // brazilian
      value: 'Brazilian',
      label: t('Brazilian'),
      icon: 'i-emojione-flag-for-brazil',
      languages: ['portuguese']
    },
    {
      // Turkish
      value: 'Turkish',
      label: t('Turkish'),
      icon: 'i-emojione-flag-for-turkey',
      languages: ['turkish']
    },
    {
      // istanbul
      value: 'Istanbul',
      label: t('Istanbul'),
      icon: 'i-emojione-flag-for-turkey',
      languages: ['turkish']
    },
    {
      // bavarian
      value: 'Bavarian',
      label: t('Bavarian'),
      icon: 'i-emojione-flag-for-germany',
      languages: ['german']
    },
    {
      // polish
      value: 'Polish',
      label: t('Polish'),
      icon: 'i-emojione-flag-for-poland',
      languages: ['polish']
    },
    {
      // italian
      value: 'Italian',
      label: t('Italian'),
      icon: 'i-emojione-flag-for-italy',
      languages: ['italian']
    },
    {
      // australian
      value: 'Australian',
      label: t('Australian'),
      icon: 'i-emojione-flag-for-australia',
      languages: ['english']
    },
    {
      // south african
      value: 'South African',
      label: t('South African'),
      icon: 'i-emojione-flag-for-south-africa',
      languages: ['english']
    },
    {
      // scottish
      value: 'Scottish',
      label: t('Scottish'),
      icon: 'i-emojione-flag-for-united-kingdom',
      languages: ['english']
    },
    {
      // welsh
      value: 'Welsh',
      label: t('Welsh'),
      icon: 'i-emojione-flag-for-united-kingdom',
      languages: ['english']
    },
    {
      // new zealand
      value: 'New Zealand',
      label: t('New Zealand'),
      icon: 'i-emojione-flag-for-new-zealand',
      languages: ['english']
    },
    {
      // dutch
      value: 'Dutch',
      label: t('Dutch'),
      icon: 'i-emojione-flag-for-netherlands',
      languages: ['dutch']
    },
    {
      // belgian
      value: 'Belgian',
      label: t('Belgian'),
      icon: 'i-emojione-flag-for-belgium',
      languages: ['dutch']
    },
    {
      // swedish
      value: 'Swedish',
      label: t('Swedish'),
      icon: 'i-emojione-flag-for-sweden',
      languages: ['swedish']
    },
    {
      // norwegian
      value: 'Norwegian',
      label: t('Norwegian'),
      icon: 'i-emojione-flag-for-norway',
      languages: ['norwegian']
    },
    {
      // danish
      value: 'Danish',
      label: t('Danish'),
      icon: 'i-emojione-flag-for-denmark',
      languages: ['danish']
    },
    {
      // italian
      value: 'Italian',
      label: t('Italian'),
      icon: 'i-emojione-flag-for-italy',
      languages: ['italian']
    },
    {
      // korean
      value: 'Korean',
      label: t('Korean'),
      icon: 'i-emojione-flag-for-south-korea',
      languages: ['korean']
    },
    {
      // korean, seoul
      value: 'Korean, Seoul',
      label: t('Korean, Seoul'),
      icon: 'i-emojione-flag-for-south-korea',
      languages: ['korean']
    },
    {
      // japanese
      value: 'Japanese',
      label: t('Japanese'),
      icon: 'i-emojione-flag-for-japan',
      languages: ['japanese']
    },
    {
      // dutch
      value: 'Dutch',
      label: t('Dutch'),
      icon: 'i-emojione-flag-for-netherlands',
      languages: ['dutch']
    },
    {
      // croatian
      value: 'Croatian',
      label: t('Croatian'),
      icon: 'i-emojione-flag-for-croatia',
      languages: ['croatian']
    },
    {
      // czech
      value: 'Czech',
      label: t('Czech'),
      icon: 'i-emojione-flag-for-czechia',
      languages: ['czech']
    },
    {
      // moravian
      value: 'Moravian',
      label: t('Moravian'),
      icon: 'i-emojione-flag-for-czechia',
      languages: ['czech']
    },
    {
      // zealandic
      value: 'Zealandic',
      label: t('Zealandic'),
      icon: 'i-emojione-flag-for-denmark',
      languages: ['danish']
    },
    {
      // indonesian
      value: 'Indonesian',
      label: t('Indonesian'),
      icon: 'i-emojione-flag-for-indonesia',
      languages: ['indonesian']
    },
    {
      // javanese
      value: 'Javanese',
      label: t('Javanese'),
      icon: 'i-emojione-flag-for-indonesia',
      languages: ['indonesian']
    },
    {
      // romanian
      value: 'Romanian',
      label: t('Romanian'),
      icon: 'i-emojione-flag-for-romania',
      languages: ['romanian']
    },
    {
      // swiss
      value: 'Swiss',
      label: t('Swiss'),
      icon: 'i-emojione-flag-for-switzerland',
      languages: ['swiss']
    },
    {
      // vietnamese
      value: 'Vietnamese',
      label: t('Vietnamese'),
      icon: 'i-emojione-flag-for-vietnam',
      languages: ['vietnamese']
    },
    {
      value: 'Arabic',
      label: t('Arabic'),
      icon: 'i-emojione-flag-for-saudi-arabia',
      languages: ['arabic']
    },
    {
      value: 'Bulgarian',
      label: t('Bulgarian'),
      icon: 'i-emojione-flag-for-bulgaria',
      languages: ['bulgarian']
    },
    {
      value: 'Finnish',
      label: t('Finnish'),
      icon: 'i-emojione-flag-for-finland',
      languages: ['finnish']
    },
    {
      value: 'Greek',
      label: t('Greek'),
      icon: 'i-emojione-flag-for-greece',
      languages: ['greek']
    },
    {
      value: 'Hungarian',
      label: t('Hungarian'),
      icon: 'i-emojione-flag-for-hungary',
      languages: ['hungarian']
    },
    {
      value: 'Filipino',
      label: t('Filipino'),
      icon: 'i-emojione-flag-for-philippines',
      languages: ['filipino']
    }
  ]

  const getAccentByValue = (value: string) => {
    return voiceAccents().find(accent => accent.value?.toLowerCase() === value?.toLowerCase())
  }

  return {
    voices: computed(() => store.voices),
    selectedVoice: computed({
      get: () => store.selectedVoice,
      set: value => store.selectedVoice = value
    }),
    loading: computed(() => store.loading),
    error: computed(() => store.error),
    loadVoices: store.loadVoices,
    selectVoice: store.selectVoice,
    toggleFavorite: store.toggleFavorite,
    deleteCustomVoice: store.deleteCustomVoice,
    favoriteVoices: computed(() => store.favoriteVoices),
    voicesByGender: computed(() => store.voicesByGender),
    premiumVoices: computed(() => store.premiumVoices),
    freeVoices: computed(() => store.freeVoices),
    updatings: computed(() => store.updatings),
    voiceAccents,
    getAccentByValue
  }
}

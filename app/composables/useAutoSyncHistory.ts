import { ref, onUnmounted } from 'vue'

interface AutoSyncOptions {
  uuid: string
  intervalMs?: number
  maxDurationMs?: number
  targetStatuses?: number[]
  onStatusChange?: (status: number, historyDetail: any) => void
  onComplete?: (historyDetail: any) => void
  onError?: (error: any) => void
}

export function useAutoSyncHistory() {
  const activeTimers = ref<Map<string, NodeJS.Timeout>>(new Map())
  const activeSyncs = ref<Set<string>>(new Set())

  const startAutoSync = (options: AutoSyncOptions) => {
    const {
      uuid,
      intervalMs = 30000, // 30 seconds
      maxDurationMs = 300000, // 5 minutes
      targetStatuses = [2, 3], // Complete or Error status
      onStatusChange,
      onComplete,
      onError
    } = options

    // Don't start if already syncing this UUID
    if (activeSyncs.value.has(uuid)) {
      console.log(`🚀 ~ Auto sync already active for UUID: ${uuid}`)
      return
    }

    console.log(`🚀 ~ Starting auto sync for UUID: ${uuid}`)
    activeSyncs.value.add(uuid)

    const historyStore = useHistoryStore()
    const startTime = Date.now()
    let attemptCount = 0

    const syncAttempt = async () => {
      try {
        attemptCount++
        console.log(`🚀 ~ Sync attempt ${attemptCount} for UUID: ${uuid}`)

        const historyDetail = await historyStore.syncHistory(uuid)

        if (historyDetail) {
          const currentStatus = historyDetail.status
          console.log(`🚀 ~ Current status for UUID ${uuid}: ${currentStatus}`)

          // Call status change callback
          if (onStatusChange) {
            onStatusChange(currentStatus, historyDetail)
          }

          // Check if we've reached target status
          if (targetStatuses.includes(currentStatus)) {
            console.log(`🚀 ~ Target status reached for UUID ${uuid}: ${currentStatus}`)
            stopAutoSync(uuid)
            if (onComplete) {
              onComplete(historyDetail)
            }
            return
          }
        }

        // Check if max duration exceeded
        const elapsed = Date.now() - startTime
        if (elapsed >= maxDurationMs) {
          console.log(`🚀 ~ Max duration exceeded for UUID ${uuid}`)
          stopAutoSync(uuid)
          return
        }

        // Schedule next sync
        const timer = setTimeout(syncAttempt, intervalMs)
        activeTimers.value.set(uuid, timer)
      } catch (error) {
        console.error(`🚀 ~ Sync error for UUID ${uuid}:`, error)
        if (onError) {
          onError(error)
        }

        // Check if max duration exceeded
        const elapsed = Date.now() - startTime
        if (elapsed >= maxDurationMs) {
          console.log(`🚀 ~ Max duration exceeded after error for UUID ${uuid}`)
          stopAutoSync(uuid)
          return
        }

        // Continue syncing even after error
        const timer = setTimeout(syncAttempt, intervalMs)
        activeTimers.value.set(uuid, timer)
      }
    }

    // Start first sync attempt
    syncAttempt()
  }

  const stopAutoSync = (uuid: string) => {
    console.log(`🚀 ~ Stopping auto sync for UUID: ${uuid}`)

    // Clear timer
    const timer = activeTimers.value.get(uuid)
    if (timer) {
      clearTimeout(timer)
      activeTimers.value.delete(uuid)
    }

    // Remove from active syncs
    activeSyncs.value.delete(uuid)
  }

  const stopAllAutoSyncs = () => {
    console.log('🚀 ~ Stopping all auto syncs')

    // Clear all timers
    activeTimers.value.forEach((timer, uuid) => {
      clearTimeout(timer)
      console.log(`🚀 ~ Cleared timer for UUID: ${uuid}`)
    })

    // Clear all tracking
    activeTimers.value.clear()
    activeSyncs.value.clear()
  }

  const isAutoSyncing = (uuid: string) => {
    return activeSyncs.value.has(uuid)
  }

  const getActiveSyncs = () => {
    return Array.from(activeSyncs.value)
  }

  // Cleanup on unmount
  onUnmounted(() => {
    stopAllAutoSyncs()
  })

  return {
    startAutoSync,
    stopAutoSync,
    stopAllAutoSyncs,
    isAutoSyncing,
    getActiveSyncs,
    activeSyncs: readonly(activeSyncs),
    activeTimers: readonly(activeTimers)
  }
}

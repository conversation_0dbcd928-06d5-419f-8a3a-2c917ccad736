# VoicesLibraries Activation Logic Test

## Test Cases for Account Activation Feature

### Test Case 1: User Not Logged In
**Scenario**: User visits My Voices or Favorite Voices tab without being logged in
**Expected**: Should show login prompt with login/register buttons
**Logic**: `needsAuthentication` should be true, `shouldShowLoginPrompt` should be true

### Test Case 2: User Logged In but Account Not Activated
**Scenario**: User is logged in but `user.is_active` is false
**Expected**: Should show activation prompt with resend email button
**Logic**: `needsAccountActivation` should be true, `shouldShowActivationPrompt` should be true

### Test Case 3: User Logged In and Account Activated
**Scenario**: User is logged in and `user.is_active` is true
**Expected**: Should show normal voice library content
**Logic**: Both `needsAuthentication` and `needsAccountActivation` should be false

### Test Case 4: Resend Activation Email Countdown
**Scenario**: User clicks "Resend Activation Email" button
**Expected**: 
- Button should be disabled for 30 seconds
- Button text should show countdown: "Resend Activation Email (30s)", "Resend Activation Email (29s)", etc.
- After 30 seconds, button should be enabled again
- Success toast should appear

### Test Case 5: Resend Email Error Handling
**Scenario**: Resend activation email fails
**Expected**: 
- Countdown should reset to 0
- Button should be enabled again
- Error should be handled gracefully

### Test Case 6: Component Unmount During Countdown
**Scenario**: User navigates away while countdown is active
**Expected**: Interval should be cleared to prevent memory leaks

## Manual Testing Steps

1. **Setup Test Environment**:
   - Create test user account that is not activated
   - Login with the test account
   - Navigate to Speech Gen page

2. **Test Login Prompt**:
   - Logout
   - Go to My Voices tab
   - Verify login prompt appears

3. **Test Activation Prompt**:
   - Login with unactivated account
   - Go to My Voices tab
   - Verify activation prompt appears
   - Verify "Resend Activation Email" and "Check Email" buttons are present

4. **Test Countdown Logic**:
   - Click "Resend Activation Email"
   - Verify button becomes disabled
   - Verify countdown appears in button text
   - Wait for countdown to complete
   - Verify button becomes enabled again

5. **Test Email Client Opening**:
   - Click "Check Email" button
   - Verify default email client opens (or mailto: link is triggered)

## Key Components to Verify

### Computed Properties
- `needsAuthentication`: Should be true when user is not logged in and on auth-required tabs
- `needsAccountActivation`: Should be true when user is logged in but not activated and on auth-required tabs
- `shouldShowLoginPrompt`: Should be true when `needsAuthentication` is true and no voices loaded
- `shouldShowActivationPrompt`: Should be true when `needsAccountActivation` is true and no voices loaded

### State Management
- `canResendActivationEmailAfter`: Should countdown from 30 to 0
- `resendActivationEmailInterval`: Should be properly managed (created/cleared)

### Functions
- `resendActivationEmail()`: Should set countdown, call API, show toast
- `openEmailClient()`: Should trigger mailto: link

## Expected Behavior Summary

1. **Not logged in** → Show login prompt
2. **Logged in but not activated** → Show activation prompt with countdown logic
3. **Logged in and activated** → Show normal content
4. **Countdown active** → Button disabled with timer display
5. **Countdown finished** → Button enabled again
